import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {AbstractControl, FormControl, FormGroup, ReactiveFormsModule, Validators} from "@angular/forms";
import { finalize } from "rxjs";
import { CustomerService } from 'src/app/@shared/services/customer.service';
import {_CRM_AFF_0, _CRM_AFF_2, _CRM_AFF_5, _CRM_CUS_4, _CRM_CUS_5, UnitDetails} from 'src/app/@shared/models/input.interfaces';
import { ToastService } from 'src/app/@core/services/toast.service';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import { BrregCompanyResponse } from 'src/app/@shared/services/companyData.service';
import {Router} from "@angular/router";
import {AffiliateContactResponse, AffiliateResponse} from "../../models/affiliate.interfaces";
import {CustomerResponse} from "../../models/customer.interfaces";
import {CompanyGeneralSettingsResponse} from "../../models/company.interfaces";
import {InvoiceSendTypeResponse} from "../../models/payment.interfaces";
import {PaymentService} from "../../services/payment.service";
import {SettingsService} from "../../services/settings.service";
import {convertCompactAddressToUnitDetails, getFormControl, UtilsService} from "../../../@core/utils/utils.service";
import {AffiliateService} from "../../services/affiliate.service";
import {createUserForm} from "../../models/forms";
import {StandardImports} from "../../global_import";
import {CompanySearchComponent} from "../company-search/company-search.component";
import {PhoneInputComponent} from "../phone-input/phone-input.component";
import {SelectoriniComponent} from "../selectorini/selectorini.component";
import {InputComponent} from "../input/input.component";
import {AddressSearchComponent} from "../address-search/address-search.component";
import {SpinnerComponent} from "../spinner/spinner.component";

@Component({
  selector: 'app-business-customer-modal',
  templateUrl: './business-customer-modal.component.html',
  styleUrls: ['./business-customer-modal.component.css'],
  standalone: true,
  imports: [StandardImports, CompanySearchComponent, PhoneInputComponent, SelectoriniComponent, InputComponent, AddressSearchComponent, SpinnerComponent]
})
export class BusinessCustomerModalComponent implements OnInit {
  @Input() customer_id: number = -1;
  @Input() showModalHeader: boolean = true;
  @Input() redirect = true
  @Output() customerAdded: EventEmitter<AffiliateResponse> = new EventEmitter<AffiliateResponse>()
  @Output() businessAdded: EventEmitter<CustomerResponse> = new EventEmitter<CustomerResponse>()
  @Input() generalSettings: CompanyGeneralSettingsResponse;

  businessCustomerForm: FormGroup;
  contactForm: FormGroup
  saving: boolean = false;
  customerAddress: UnitDetails
  customerAddressValid: boolean = false;
  canUseEHF: boolean = false;
  invoiceSendTypes: InvoiceSendTypeResponse[] = [];
  disabledInvoiceSendTypes: InvoiceSendTypeResponse[] = [];
  selectedInvoiceSendType: InvoiceSendTypeResponse;
  hasAccounting: boolean = false;
  showContactForm: boolean = false;
  phoneValid: boolean = false;

  constructor(public activeModal: NgbActiveModal,
      private toastService: ToastService,
      private customerService: CustomerService,
      private paymentService: PaymentService,
      private settingsService: SettingsService,
      private utilsService: UtilsService,
      private router: Router,
      private affiliateService: AffiliateService
  ) {}

  ngOnInit() {
    this.businessCustomerForm = new FormGroup({
        customer_name: new FormControl<string>('', [Validators.required]),
        organisation_number: new FormControl<string>('', [Validators.required]),
        phone: new FormControl<string | null>(null),
        email: new FormControl<string>('', [Validators.email]),
        invoiceEmail: new FormControl<string>('', [Validators.email]),
        accounting_id: new FormControl<string>(''),
        invoice_due_date_days: new FormControl<number>(this.generalSettings?.default_business_customers_due_date || 14, [Validators.required, Validators.min(0), Validators.max(365)]),
      });

    this.settingsService.getCompanyGeneralSettings().subscribe((res) => {
      this.hasAccounting = res.accounting_system_id !== null;
      if (!this.generalSettings) {
        this.generalSettings = res;
      }
    });


    this.contactForm = createUserForm();
    this.contactForm.addControl('role_description', new FormControl<string>(''));

    this.paymentService.getInvoiceSendTypes().subscribe((res) => {
      this.invoiceSendTypes = res;
      this.invoiceSendTypes.map((type) => {
        if (type.invoice_send_type_id != 0) {
          this.disabledInvoiceSendTypes.push(type);
        }
      });

      this.invoiceSendTypes.map((type) => {
        if (type.invoice_send_type_id === 0) {
          this.selectedInvoiceSendType = type;
        }
      });
    });

    if(this.customer_id != -1){
      this.fetchCustomerData(this.customer_id);
    }
  }

  handleCompanySelected(company: BrregCompanyResponse): void {
    this.setEhfAccess(company.organisationNumber);

    this.utilsService.getAddressFromBrregResponse(company).subscribe((address) => {
      if (!address) {
        return;
      }
      this.customerAddress = address;
      this.customerAddressValid = true;
    });

    this.businessCustomerForm.patchValue({
      customer_name: company.name,
      organisation_number: company.organisationNumber,
      phone: null,
      email: '',
    });
  }
  fetchCustomerData(customer_id: number){
    this.customerService.getBusinessCustomerById(customer_id).subscribe((res) => {
      this.invoiceSendTypes.map((type) => {
        if (type.invoice_send_type_id === res.invoice_send_type_id) {
          this.selectedInvoiceSendType = type;
        }
      });

      this.setEhfAccess(res.organisation_number);

      this.customerAddress = convertCompactAddressToUnitDetails(res.address)!;
      this.customerAddressValid = true;

      this.businessCustomerForm.patchValue({
        customer_name: res.name,
        organisation_number: res.organisation_number,
        phone: res.phone,
        email: res.email,
        accounting_id: res.accounting_id,
        invoiceEmail: res.invoice_email,
      });
    })
  }

  setEhfAccess(organisationNumber: string | null) {
    if (!organisationNumber) {
      return;
    }
    this.customerService.checkPeppolEHF(organisationNumber).subscribe((res) => {
      this.disabledInvoiceSendTypes = []
      this.canUseEHF = res.can_receive_ehf === 1;
      this.invoiceSendTypes.map((type) => {
        if (type.invoice_send_type_id !== 0 && !this.canUseEHF) {
          this.disabledInvoiceSendTypes.push(type);
        }
      });
    });
  }


  invoiceSendTypeSelected(event: any) {
    this.selectedInvoiceSendType = event;
  }

  onAddressUpdated(addressWrapper: { id: number, address: UnitDetails }) {
    this.customerAddress = addressWrapper.address;
    this.customerAddressValid = true;
  }

  onAddressRemoved() {
    this.customerAddressValid = false;
  }

  onSubmit() {
    this.saving = true;

    let addContact = false;
    if (this.showContactForm) {
      if (this.contactForm.value.first_name || this.contactForm.value.last_name || this.contactForm.value.phone || this.contactForm.value.email) {
        addContact = true;
      }
    }

    const payload: _CRM_AFF_0 = {
      company_name: this.businessCustomerForm.value.customer_name,
      organisation_number: this.businessCustomerForm.value.organisation_number,
      phone: this.businessCustomerForm.value.phone || null,
      email: this.businessCustomerForm.value.email || null,
      accounting_id: this.businessCustomerForm.value.accounting_id,
      address: this.customerAddress,
      invoice_email: this.businessCustomerForm.value.invoiceEmail,
      invoice_send_type_id: this.selectedInvoiceSendType.invoice_send_type_id,
      invoice_due_date_days: this.businessCustomerForm.value.invoice_due_date_days,
      is_customer: 1,
    }

    console.log('addContact', addContact)


    if(this.customer_id === -1){
      this.customerService.createBusinessCustomer(payload)
      .pipe(
        finalize(() => this.saving = false)
        )
        .subscribe({
          next: (aff_result) => {
            if (aff_result.affiliate_id) {
              if (addContact) {
                let payload: _CRM_AFF_5 = {
                  affiliate_id: aff_result.affiliate_id,
                  first_name: this.contactForm.value.first_name,
                  last_name: this.contactForm.value.last_name,
                  phone: this.contactForm.value.phone || null,
                  email: this.contactForm.value.email || null,
                  role_description: this.contactForm.value.role_description,
                }
                this.affiliateService.createAffiliateContact(payload).subscribe(
                (aff_con_result: AffiliateContactResponse) => {
                  let response: CustomerResponse = {
                    affiliate_id: aff_result.affiliate_id,
                    name: aff_con_result.name,
                    email: aff_con_result.email,
                    phone: aff_con_result.phone,
                    user_id: aff_con_result.user_id,
                    is_private: 0,
                    organisation_number: aff_result.organisation_number,
                    company_name: aff_result.name,
                    affiliate_contact_id: aff_con_result.affiliate_contact_id,
                    icon_id: 2
                  }
                  this.toastService.successToast("businessAndContactCreated");
                  this.customerAdded.emit(aff_result);
                  this.businessAdded.emit(response);
                  this.activeModal.close(response);
                if (this.redirect) {
                  this.router.navigate(['/customers/business/', aff_result.affiliate_id]);
                }
                },
                (err: any) => {
                  this.toastService.errorToast("contactCreationError");
                }
              )}
              else {
                this.toastService.successToast("customerSaved");
                let response: CustomerResponse = {
                  affiliate_id: aff_result.affiliate_id,
                  name: aff_result.name,
                  email: aff_result.email,
                  phone: aff_result.phone,
                  user_id: aff_result.user_id,
                  is_private: 0,
                  organisation_number: aff_result.organisation_number,
                  company_name: null,
                  affiliate_contact_id: null,
                  icon_id: 1
                }
                this.customerAdded.emit(aff_result);
                this.businessAdded.emit(response);
                this.activeModal.close(response);
                if (this.redirect) {
                  this.router.navigate(['/customers/business/', aff_result.affiliate_id]);
                }
              }
            }
          },
          error: (e) => {
            console.log(e);
          }
        });
    }else{
    const payload: _CRM_AFF_2 = {
      affiliate_id: this.customer_id,
      _phone: this.businessCustomerForm.value.phone,
      _email: this.businessCustomerForm.value.email,
      accounting_id: this.businessCustomerForm.value.accounting_id,
      address: this.customerAddress,
      _invoice_email: this.businessCustomerForm.value.invoiceEmail,
      invoice_send_type_id: this.selectedInvoiceSendType.invoice_send_type_id,
      invoice_due_date_days: this.businessCustomerForm.value.invoice_due_date_days,
    }
      this.customerService.updateBusinessCustomer(payload)
      .pipe(
        finalize(() => this.saving = false)
        )
        .subscribe({
          next: (result) => {
            if (result.affiliate_id) {
                this.toastService.successToast("customerSaved");
                this.customerAdded.emit(result);
                this.activeModal.close();
            }
          },
          error: (e) => {
            console.log(e);
          }
        });
    }
  }
  handlePhoneNumberChange(phoneNumber: string | null): void {
    this.businessCustomerForm.patchValue({phone: phoneNumber})
  }

  handlePhoneNumberValid(isValid: boolean): void {
    this.phoneValid = isValid;
  }

  handlePhoneNumberChangeContact(phoneNumber: string | null): void {
    this.contactForm.patchValue({phone: phoneNumber})
  }

  handlePhoneNumberValidContact(isValid: boolean): void {
    this.phoneValid = isValid;
  }

  protected readonly getFormControl = getFormControl;
}
