import {Component, EventEmitter, Input, Output} from '@angular/core';
import {TranslateModule} from "@ngx-translate/core";


import {ButtonComponent} from "../button/button.component";
import {StandardImports} from "../../global_import";

@Component({
  selector: 'app-button-double',
  templateUrl: './button-double.component.html',
  styleUrls: ['./button-double.component.css'],
  standalone: true,
  imports: [StandardImports]
})
export class ButtonDoubleComponent {

  @Input() leftButtonTranslationKey: string = '';
  @Input() rightButtonTranslationKey: string = '';
  @Input() leftButtonDisabled: boolean = false;
  @Input() rightButtonDisabled: boolean = false;
  @Input() leftButtonActive: boolean = false;
  @Input() rightButtonActive: boolean = false;

  @Input() small: boolean = false;

  @Output() leftButtonClick: EventEmitter<void> = new EventEmitter<void>();
  @Output() rightButtonClick: EventEmitter<void> = new EventEmitter<void>();

  constructor() {
  }

  protected readonly crossOriginIsolated = crossOriginIsolated;
}
