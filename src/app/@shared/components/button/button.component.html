<!-- Solid Button -->
<button *ngIf="buttonType == 'solid'" type="button" #solidButton
        class="d-flex align-items-center justify-content-center"
        [class]="'solid btn ' + 'btn-' + themeStyle + ' ' + customClass + ' ' + (small ? 'no-padding' : '')"
        [ngClass]="{'dropdown-toggle': showDropdownChevron, 'feigned-disabled': feignDisabled}"
        [style.min-width.px]="buttonWidth"
        [ngStyle]="{'height.px': small ? 26 : 38, backgroundColor: backgroundColor ? backgroundColor : null}"
        [disabled]="loading || disabled"
        (click)="click($event)">
  <span *ngIf="iconClass && !loading && iconPlacement == 'left'" class="{{iconClass}}" [ngbTooltip]="iconNgbTooltipTranslationKey | translate"></span>
  <a style="color: white; text-align: center;" [style.font-weight]="[boldText ? 'bold' : 'normal']" *ngIf="!loading">{{translationKey | translate}}</a>
  <span *ngIf="iconClass && !loading && iconPlacement == 'right'" class="{{iconClass}}" [ngbTooltip]="iconNgbTooltipTranslationKey | translate"></span>
  <app-spinner *ngIf="loading"></app-spinner>
</button>

<!-- Nude Button -->
<button *ngIf="buttonType == 'nude'" type="button" #nudeButton
        [class]="'nude btn btn-outline-light' + ' ' + customClass"
        [ngClass]="{'nude-bold': boldText, 'dropdown-toggle': showDropdownChevron}"
        [style.min-width.px]="buttonWidth"
        [ngStyle]="{'height.px': small ? 26 : 38, backgroundColor: backgroundColor ? backgroundColor : null}"
        style="padding: 0 10px 0 10px; border-color: #E6E9EC;"
        [disabled]="loading || disabled"
        (click)="click($event)">
  <span *ngIf="iconClass && !loading && iconPlacement == 'left'" class="{{iconClass}}" [ngbTooltip]="iconNgbTooltipTranslationKey | translate"></span>
  <a *ngIf="!loading" style="color: #6C757D">{{translationKey | translate}}</a>
  <span *ngIf="iconClass && !loading && iconPlacement == 'right'" class="{{iconClass}}" [ngbTooltip]="iconNgbTooltipTranslationKey | translate"></span>
  <app-spinner *ngIf="loading"></app-spinner>
</button>

<!-- Outline Button -->
<button *ngIf="buttonType == 'outline'" type="button" #outlineButton
        [class]="'outline-text-color btn btn-outline-' + themeStyle + ' ' + customClass"
        [ngClass]="{'nude-bold': boldText, 'dropdown-toggle': showDropdownChevron}"
        [style.min-width.px]="buttonWidth"
        [ngStyle]="{'height.px': small ? 26 : 38, backgroundColor: backgroundColor ? backgroundColor : null}"
        style="padding: 0 10px 0 10px; border-color: #448C74; border-radius: 5px"
        [disabled]="loading || disabled"
        (click)="click($event)">
  <span *ngIf="iconClass && !loading && iconPlacement == 'left'" class="{{iconClass}}" [ngbTooltip]="iconNgbTooltipTranslationKey | translate"></span>
  <span *ngIf="!loading" [class]="'outline-text-color outline-text-' + themeStyle">{{translationKey | translate}}</span>
  <span *ngIf="iconClass && !loading && iconPlacement == 'right'" class="{{iconClass}}" [ngbTooltip]="iconNgbTooltipTranslationKey | translate"></span>
  <app-spinner *ngIf="loading"></app-spinner>
</button>

<!-- Link Button -->
<button *ngIf="buttonType == 'link'" type="button" #linkButton
        [class]="'link-button btn ' + customClass"
        [style.min-width.px]="buttonWidth"
        [ngStyle]="{'height.px': small ? 26 : 38, backgroundColor: backgroundColor ? backgroundColor : null}"
        [disabled]="loading || disabled"
        style="height: 38px; border-radius: 5px"
        (click)="click($event)">
  <a *ngIf="!loading" class="between-color clickable-text">{{translationKey | translate}}</a>
  <app-spinner *ngIf="loading"></app-spinner>
</button>

<!-- Dropdown Button (use for buttons to be shown inside a dropdown menu) -->
<button *ngIf="buttonType == 'dropdown'" type="button" #dropDownButton
        class="dropdown-item"
        [class]="'btn ' + ' ' + customClass + ' ' + (small ? 'no-padding' : '')"
        [ngClass]="{'text-muted': disabled}"
        [style.min-width.px]="buttonWidth"
        [ngStyle]="{'height.px': small ? 26 : 38, backgroundColor: backgroundColor ? backgroundColor : null}"
        [disabled]="loading || disabled"
        (click)="click($event)">
  <span *ngIf="iconClass && !loading" class="{{iconClass}}"></span>
  <span *ngIf="!loading">{{translationKey | translate}}</span>
  <app-spinner *ngIf="loading"></app-spinner>
</button>

<!-- Dropdown Caret (use to append on right side of dropdown menu button) -->
<button *ngIf="buttonType == 'dropdown-caret'" type="button" #dropdownCaretButton
        class="d-flex align-items-center justify-content-center no-left-border-radius"
        [class]="'solid btn ' + 'btn-' + themeStyle + ' ' + customClass + ' ' + (small ? 'no-padding' : '')"
        [ngClass]="{'feigned-disabled': feignDisabled}"
        [style.min-width.px]="buttonWidth"
        [ngStyle]="{'height.px': small ? 26 : 38, backgroundColor: backgroundColor ? backgroundColor : null}"
        [disabled]="loading || disabled"
        (click)="click($event)">
  <i class="fa-solid fa-caret-down" style="color: #ffffff;"></i>
  <app-spinner *ngIf="loading"></app-spinner>
</button>
