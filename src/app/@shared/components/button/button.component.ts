import {AfterViewInit, ChangeDetectorRef, Component, ElementRef, EventEmitter, Input, OnChanges, Output, SimpleChanges, ViewChild} from '@angular/core';
import {TranslateModule} from "@ngx-translate/core";
import {CommonModule, NgClass, NgIf, NgStyle} from "@angular/common";
import {SpinnerComponent} from "../spinner/spinner.component";
import {NgbTooltip} from "@ng-bootstrap/ng-bootstrap";

@Component({
  selector: 'app-button',
  templateUrl: './button.component.html',
  styleUrls: ['./button.component.css'],
  standalone: true,
  imports: [
    TranslateModule,
    CommonModule,
    SpinnerComponent,
    NgbTooltip
  ]
})


export class ButtonComponent implements OnChanges {
  @Input() buttonType: 'solid' | 'nude' | 'link' | 'dropdown' | 'outline' | 'dropdown-caret' = "solid"
  @Input() themeStyle: string = 'primary';
  @Input() customClass: string = "";
  @Input() translationKey: string = ""
  @Input() backgroundColor: string;

  @Input() iconClass: string = "";
  @Input() iconPlacement: 'left' | 'right' = 'left';
  @Input() iconNgbTooltipTranslationKey: string = "";
  @Input() customHeight: string = ""

  @Input() xsmall: boolean = false
  @Input() small: boolean = false
  @Input() buttonWidth: number | null = null;
  @Input() loading: boolean = false
  @Input() disabled: boolean = false;
  @Input() feignDisabled: boolean = false;

  @Input() showDropdownChevron: boolean = false;

  @Input() boldText: boolean = true;

  @Output() buttonClick = new EventEmitter<MouseEvent>();

  @ViewChild('solidButton') solidButton: ElementRef;
  @ViewChild('nudeButton') nudeButton : ElementRef;
  @ViewChild('linkButton') linkButton: ElementRef;
  @ViewChild('dropDownButton') dropDownButton: ElementRef;
  @ViewChild('outlineButton') outlineButton: ElementRef;
  @ViewChild('dropdownCaretButton') dropdownCaretButton: ElementRef;

  constructor(private cdr: ChangeDetectorRef) {
  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['loading']) {
      this.setButtonWidth();
    }
  }

  setButtonWidth() {
   if (!this.buttonWidth) {
      if (this.buttonType === 'solid' && this.solidButton) {
        this.buttonWidth = this.solidButton.nativeElement.offsetWidth
      } else if (this.buttonType === 'nude' && this.nudeButton) {
        this.buttonWidth = this.nudeButton.nativeElement.offsetWidth
      } else if (this.buttonType === 'link' && this.linkButton) {
        this.buttonWidth = this.linkButton.nativeElement.offsetWidth
      } else if (this.buttonType === 'dropdown' && this.dropDownButton) {
        this.buttonWidth = this.dropDownButton.nativeElement.offsetWidth
      } else if (this.buttonType === 'outline' && this.outlineButton) {
        this.buttonWidth = this.outlineButton.nativeElement.offsetWidth
      }
    }
   this.cdr.detectChanges();
  }

  click(event: MouseEvent): void {
    if (this.disabled || this.loading) {
      return;
    }
    this.buttonClick.emit(event)
  }
}
