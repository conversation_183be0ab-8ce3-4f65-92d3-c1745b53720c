<div class="modal-header d-flex justify-content-between align-items-center">
  <i class="fa-regular fa-xmark fa-xl" (click)="activeModal.close()" style="font-weight: 400; cursor: pointer"></i>
  <h4 class="text-center" style="flex-grow: 1;">{{ "customers.privateCustomers.new.editPageName" | translate }}</h4>
</div>
  <div class="modal-body p-3" *ngIf="customerDetails">
    <form [formGroup]="customerForm">
      <div class="form-group row mb-2">
        <p *ngIf="!editable">NB: {{"customers.privateCustomers.notEditable" | translate}}</p>
        <div class="form-group col-6">
          <app-input
            type="text"
            [labelKey]="'customers.privateCustomers.firstName'"
            [control]="getFormControl(customerForm, 'first_name')"
            [editMode]="true"
          ></app-input>
        </div>

        <div class="form-group col-6">
          <app-input
            type="text"
            [labelKey]="'customers.privateCustomers.lastName'"
            [control]="getFormControl(customerForm, 'last_name')"
            [editMode]="true"
          ></app-input>
        </div>
      </div>

      <div class="form-group row mb-2">
        <div class="form-group col-6">
          <app-input
            type="email"
            [labelKey]="'orders.newOrder.newCustomer.email'"
            [control]="getFormControl(customerForm, 'email')"
            [editMode]="true"
            [showErrorIcon]="false"
          ></app-input>
        </div>

        <div class="form-group col-6">
          <label for="phone">{{ "orders.newOrder.newCustomer.phone" | translate }}</label>
          <!-- <app-input
            type="text"
            [labelKey]="'orders.newOrder.newCustomer.phone'"
            [control]="getFormControl(customerForm, 'phone')"
            [editMode]="true"
            [inputPrefix]="'+47'"

            oninput="if(this.value.length > 8) this.value = this.value.slice(0, 8);"
            [showErrorIcon]="false"
          ></app-input> -->

          <app-phone-input
            id="phone"
            [initialPhoneNumber]="customerDetails.phone ? customerDetails.phone : ''"
            (phoneNumberChange)="handlePhoneNumberChange($event)"
            (phoneNumberValid)="handlePhoneNumberValid($event)">
          </app-phone-input>

        </div>
      </div>
    </form>

    <div class="row">
      <div class="col-6" *ngIf="accountingEnabled">
        {{ "customers.privateCustomers.accountingLabel" | translate }}
        <app-selectorini
          [zIndex]="100"
          [selectedItem]="selectedAccountingCustomer"
          [predefinedSearchResults]="accountingCustomers"
          [predefinedSearchKeys]="['display_name']"
          [searchMainDisplayKeys]="['display_name']"
          [searchSubDisplayKeys]="['organisation_number']"
          [selectedMainDisplayKeys]="['display_name']"
          [placeholderTranslationKey]="'customers.privateCustomers.accountingPlaceholder'"
          [showIcons]="false"
          [disableOnSelect]="true"
          [loading]="accountingLoading"
          (itemSelectedEmitter)="accountingCustomerSelected($event)"
          (itemDeselectedEmitter)="accountingCustomerDeselected()">
        </app-selectorini>
      </div>

      <div class="form-group col-6">
          <app-input
            type="text"
            [labelKey]="'customers.privateCustomers.invoiceDueDate'"
            [inputSuffix]="'common.days' | translate"
            [control]="invoiceDueDateControl"
            [editMode]="true"

            [showErrorIcon]="false"
          ></app-input>
        </div>

    </div>

  </div>
  <div class="modal-footer justify-content-end pe-3">
    <button type="button" class="btn btn-secondary m-0" (click)="activeModal.close()">{{ "common.cancel" | translate }}</button>
    <button type="submit" class="btn btn-primary m-0 ms-2" [disabled]="!phoneValid || invoiceDueDateControl.invalid"   (click)="onSubmit()">
      <app-spinner *ngIf="saving"/>
      <span *ngIf="!saving">{{ "common.save" | translate }}</span></button>
  </div>

