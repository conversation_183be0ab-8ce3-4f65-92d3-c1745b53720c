import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {ActivatedRoute} from "@angular/router";
import {AbstractControl, FormBuilder, FormControl, FormGroup, Validators} from "@angular/forms";
import {
  convertCompactAddressToUnitDetails,
  getFormControl,
  UtilsService
} from "../../../@core/utils/utils.service";
import {NgbActiveModal} from "@ng-bootstrap/ng-bootstrap";
import {ToastService} from "../../../@core/services/toast.service";
import {CustomerAccountingResponse} from "../../models/customer.interfaces";
import {_CRM_CUS_12} from "../../models/input.interfaces";
import {CustomerService} from "../../services/customer.service";
import {AffiliateResponse} from "../../models/affiliate.interfaces";
import {createUserForm, UserDataInterface} from "../../models/forms";
import {StandardImports} from "../../global_import";
import {PhoneInputComponent} from "../phone-input/phone-input.component";
import {SelectoriniComponent} from "../selectorini/selectorini.component";
import {SpinnerComponent} from "../spinner/spinner.component";

@Component({
    selector: 'app-customer-modal',
    templateUrl: './edit-customer-modal.component.html',
    styleUrls: ['./edit-customer-modal.component.css'],
    standalone: true,
  imports: [StandardImports, PhoneInputComponent, SelectoriniComponent, SpinnerComponent]
})
export class EditCustomerModalComponent implements OnInit {
  @Input() affiliateId: number;
  @Input() accountingEnabled: boolean;
  @Output() customerUpdateResult: EventEmitter<AffiliateResponse> = new EventEmitter<AffiliateResponse>();

  saving: boolean = false;
  active: number;
  editable: boolean = false;
  customerDetails: AffiliateResponse;
  selectedAccountingCustomer: CustomerAccountingResponse | null = null;
  accountingCustomers: CustomerAccountingResponse[] = [];
  invoiceDueDateControl: FormControl = new FormControl(14, [Validators.required, Validators.min(1), Validators.max(365), Validators.pattern("^[0-9]*$")]);
  phoneValid: boolean = false;
  accountingLoading: boolean = false;

  customerForm: FormGroup;


  constructor(public utilsService: UtilsService,
              private customerService: CustomerService,
              public activeModal: NgbActiveModal,
              private toastService: ToastService,
              ) {
  }

  ngOnInit() {

    // Get customer details
    this.customerService.getPrivateCustomerByCustomerId(this.affiliateId).subscribe(res => {
      this.customerDetails = res;

      let firstName = '';
      let lastName = '';
      if (res.name) {
        const names = res.name.split(' ');
        if (names.length === 1) {
          firstName = names[0]; // If only one word, set it as the first name
        } else {
          firstName = names.slice(0, -1).join(' '); // Join all words except the last one as the first name
          lastName = names[names.length - 1] || ''; // Get the last word as the last name
        }
      }

       let userData: UserDataInterface = {
        first_name: firstName || '',
        last_name: lastName || '',
        phone: res.phone || '',
        email: res.email || '',
      }
      this.customerForm = createUserForm(userData);

      // this.customerForm = new FormGroup({
      //   firstName: new FormControl(firstName, [Validators.required]),
      //   lastName: new FormControl(lastName, [Validators.required]),
      //   phone: new FormControl(this.customerDetails.phone?.startsWith('+47') ? this.customerDetails.phone.substring(3) : this.customerDetails.phone, [Validators.required, Validators.minLength(8), Validators.maxLength(8)]),
      //   email: new FormControl(this.customerDetails.email, [Validators.required, Validators.email]),
      // });

      // Check if customer can be edited
      this.customerService.canEditCustomer(this.customerDetails.user_id!).subscribe(res => {
        this.editable = res.editable;
        if (!this.editable) {
          this.customerForm.controls['first_name'].disable();
          this.customerForm.controls['last_name'].disable();
        }
      });

      this.invoiceDueDateControl.setValue(this.customerDetails.invoice_due_date_days);

      if (this.accountingEnabled) {
        this.accountingLoading = true;
        this.customerService.getAccountingCustomers().subscribe(res => {
          this.accountingCustomers = res;
          for (const customer of this.accountingCustomers) {
            if (customer.customer_id.toString() === this.customerDetails.accounting_id) {
              this.selectedAccountingCustomer = customer;
            }
          }
          this.accountingLoading = false;
        }, error => {
          this.accountingLoading = false;
        });
      }

    });
  }

  accountingCustomerSelected(event: any) {
    this.selectedAccountingCustomer = event;
  }

  accountingCustomerDeselected() {
    this.selectedAccountingCustomer = null;
  }

  onSubmit() {
    if (!this.customerForm.valid) {
      this.customerForm.markAllAsTouched();
      return
    }
    this.saving = true;

    let payload: _CRM_CUS_12 = {
      affiliate_id: this.customerDetails.affiliate_id,
      first_name: this.customerForm.get('first_name')!.value,
      last_name: this.customerForm.get('last_name')!.value,
      phone: this.customerForm.get('phone')!.value,
      email: this.customerForm.get('email')!.value,
      accounting_id: this.selectedAccountingCustomer ? this.selectedAccountingCustomer.customer_id : null,
      sub_ledger_account_id: this.selectedAccountingCustomer ? this.selectedAccountingCustomer.sub_ledger_account_id : null,
      invoice_due_date_days: this.invoiceDueDateControl.value,
    }


    this.customerService.updatePrivateCustomer(payload).subscribe({
      next: (res) => {
        this.saving = false;  // Stop spinner when successful
        this.activeModal.close(res);
      },
      error: (error) => {
        this.toastService.errorToast("duplicate_phone")
        this.saving = false;  // Stop spinner on error
      }
    });
  }

  handlePhoneNumberChange(phoneNumber: string | null): void {
    this.customerForm.patchValue({phone: phoneNumber})
  }

  handlePhoneNumberValid(isValid: boolean): void {
    this.phoneValid = isValid;
  }


  protected readonly getFormControl = getFormControl;
}
