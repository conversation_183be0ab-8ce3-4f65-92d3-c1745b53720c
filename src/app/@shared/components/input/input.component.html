<div class="form-group input-icon-container" [ngClass]="outerDivClasses" style="margin-bottom: 0 !important; padding-bottom: 0 !important;">
  <label *ngIf="labelKey" [ngClass]="{'fw-bold': !editMode}">
    {{ labelKey | translate }}
    <i [ngbTooltip]="tooltipText" [ngClass]="tooltipIcon" *ngIf="isTooltip"></i>
    <span *ngIf="showAsRequired" class="text-danger">*</span>
  </label>
  <div class="input-group position-relative" [ngClass]="innerDivClasses">
    <span class="" [ngClass]="!editMode ? ['d-flex', 'align-items-center', 'ps-0', 'pe-1'] : 'input-group-text'" *ngIf="inputPrefix">{{ inputPrefix }}</span>
    <div *ngIf="!editMode" style="padding-left: 2px;"></div>
    <input
      *ngIf="!textArea"
      #inputField
      noScroll
      [type]="type"
      [formControl]="control"
      [placeholder]="placeholderKey | translate"
      [readonly]="isReadonly"
      class="form-control"
      [style.min-width.px]="inputFieldMinWidthPx"
      [style.max-width.px]="inputFieldMaxWidthPx"
      [ngStyle]="{
        'cursor': editMode ? '' : (!isReadonly && !editMode ? 'pointer' : 'auto'),
        'padding-left': centerWithNoPadding ? '0' : '',
        'padding-right': centerWithNoPadding ? '0' : (rightPadding || ''),
        'padding-top': noYPadding ? '0' : '',
        'padding-bottom': noYPadding ? '0' : '',
      }"
      [ngClass]="[
        control.invalid && control.touched ? 'red-border' : '',
        !editMode ? 'edit-mode-borders' : '',
        !editMode && inputPrefix ? 'ps-0': '',
        centerWithNoPadding ? 'text-center': '',
        !editMode ? readInputClasses.join(' ') : '',
        editMode ? editInputClasses.join(' ') : '',
        noRightBorderRadius ? 'no-right-border-radius' : '',
        noLeftBorderRadius ? 'no-left-border-radius' : '',
        noBottomBorderRadius ? 'no-bottom-border-radius' : '',
        noTopBorderRadius ? 'no-top-border-radius' : '',
        hideTypeIcon && type === 'time' ? 'hide-time-icon' : '',
      ]"

      (keydown)="preventDecimalInput($event); onKeyDownEvent($event)"
      (input)="sanitizeInput($event)"
      (blur)="onBlur()"
      (wheel)="preventScroll($event)"
      (focusin)="onFocusIn($event)"
      (focusout)="onFocusOut($event)"
    >
    <textarea
      *ngIf="textArea"
      #textAreaField
      [cdkTextareaAutosize]="true"
      [cdkAutosizeMaxRows]="textAreaMaxRows"
      [rows]="textAreaRows"
      class="form-control"
      [formControl]="control"
      [placeholder]="placeholderKey | translate"
      (keydown)="preventDecimalInput($event); onKeyDownEvent($event)"
      (input)="sanitizeInput($event)"
      (blur)="onBlur()"
      (wheel)="preventScroll($event)"
      (focusin)="onFocusIn($event)"
      (focusout)="onFocusOut($event)"
    ></textarea>
    <div *ngIf="showInputButtons && editMode" class="d-flex justify-content-center align-items-center position-absolute" style="top: 9px; right: 12px; z-index: 100">
      <div class="input-button border rounded-3 d-flex justify-content-center align-items-center me-1 cursor-pointer" (click)="checkButtonClicked.emit()" style="max-width: 20px; min-height: 20px; z-index: 1000">
        <i class="fa-regular fa-check fa-xs p-1"></i>
      </div>
      <div class="input-button border rounded-3 d-flex justify-content-center align-items-center cursor-pointer" (click)="crossButtonClicked.emit()" style="max-width: 20px; min-height: 20px; z-index: 1000">
        <i class="fa-regular fa-x fa-xs p-1"></i>
      </div>
    </div>
    <span class="input-group-text" *ngIf="inputSuffix">{{ inputSuffix }}</span>
    <i *ngIf="showErrorIcon && control?.invalid && (control?.touched || control?.dirty)" class="fa-regular fa-circle-exclamation input-icon" [ngbTooltip]="getErrorMessage()"></i>
  </div>
  <div *ngIf="!editMode"></div>
  <div *ngIf="showErrorMessage && control?.invalid && (control?.dirty || control?.touched)" class="text-danger" style="padding-top: 1px;">
    {{ getErrorMessage() }}
  </div>
</div>


<!-- Example from -->
<!--
  <form [formGroup]="companyForm" (ngSubmit)="onSubmit()" enctype="multipart/form-data">

            <app-input [control]="companyForm.controls.company_name"
                       labelKey="superadmin.companies.companyName"
                       placeholderKey="superadmin.companies.companyName">
            </app-input>
          </div>
        </div>

        <div class="row mb-3">
          <div class="col">
            <app-input [control]="companyForm.controls.phone"
                       labelKey="superadmin.companies.phone"
                       placeholderKey="superadmin.companies.phone"
                       inputPrefix="+47"
                       type="number">
            </app-input>
    <button type="submit" [disabled]="loading" class="btn btn-primary float-end mb-2">{{ "employees.employee-details.save" | translate }}</button>
  </form> -->

    <!-- Example from with Lazy Initialization in ngOnInit -->
<!--
  <form [formGroup]="companyForm" (ngSubmit)="onSubmit()" enctype="multipart/form-data">

            <app-input [control]="companyForm.controls.company_name"
                       labelKey="superadmin.companies.companyName"
                       placeholderKey="superadmin.companies.companyName">
            </app-input>
          </div>
        </div>

        <div class="row mb-3">
          <div class="col">
            <app-input [control]="companyForm.controls.phone"
                       labelKey="superadmin.companies.phone"
                       placeholderKey="superadmin.companies.phone"
                       inputPrefix="+47"
                       type="number">
            </app-input>
    <button type="submit" [disabled]="loading" class="btn btn-primary float-end mb-2">{{ "employees.employee-details.save" | translate }}</button>
  </form> -->
