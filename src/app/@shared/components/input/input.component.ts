import {Component, Input, Output, OnInit, OnDestroy, EventEmitter, ViewChild, ElementRef, AfterViewInit} from '@angular/core';
import {FormControl, FormsModule, ReactiveFormsModule, Validators} from '@angular/forms';
import {TranslateModule, TranslateService} from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import {CommonModule} from "@angular/common";
import {NgbTooltip} from "@ng-bootstrap/ng-bootstrap";
import {CdkTextareaAutosize} from "@angular/cdk/text-field";

type InputType = 'text' | 'number' | 'email' | 'password' | 'tel' | 'date' | 'time' | 'datetime-local' | 'month' | 'week' | 'color' | 'range' | 'url' | 'search' | 'file';


@Component({
  selector: 'app-input',
  templateUrl: './input.component.html',
  styleUrls: ['./input.component.css'],
  standalone: true,
  imports: [CommonModule, FormsModule, NgbTooltip, TranslateModule, CdkTextareaAutosize, ReactiveFormsModule]
})
export class InputComponent implements OnInit, OnDestroy, AfterViewInit {
  isBlurred: boolean = false;
  @Input() validateOnBlur: boolean = true;
  @Input() labelKey: string = '';
  @Input() placeholderKey: string = '';
  @Input() type: InputType = 'text';
  @Input() validationMessages: { [key: string]: string } = {};
  @Input() inputPrefix: string | null = null;
  @Input() inputSuffix: string | null = null;
  @Input() isReadonly: boolean = false;
  @Input() control = new FormControl
  @Input() removeSpaces: boolean = false;
  @Input() editMode: boolean = true;
  @Input() showAsRequired: boolean = false;
  @Input() outerDivClasses: string[] = [];
  @Input() innerDivClasses: string[] = [];
  @Input() readInputClasses: string[] = [];
  @Input() editInputClasses: string[] = [];
  @Input() centerWithNoPadding: boolean = false;
  @Input() controlClassName: 'phone' | 'first-name' | 'last-name' | 'email' | 'name' | 'organisation-number' | 'address' | 'zip-code' | 'city' | null = null;
  @Input() showErrorIcon: boolean = false;
  @Input() showErrorMessage: boolean = true;
  @Input() hideDecimals: boolean = false;
  @Input() maxDecimals: number | null = null;
  @Input() emitChangeOnBlurOnly: boolean = false;
  @Input() rightPadding: string = '';
  @Input() noLeftBorderRadius: boolean = false;
  @Input() noRightBorderRadius: boolean = false;
  @Input() noBottomBorderRadius: boolean = false;
  @Input() noTopBorderRadius: boolean = false;
  @Input() hideTypeIcon: boolean = false;
  @Input() noYPadding: boolean = false;
  @Input() textArea: boolean = false;
  @Input() takeFocus: boolean = false;
  @Input() inputFieldMinWidthPx: number | null = null;
  @Input() inputFieldMaxWidthPx: number | null = null;
  @Input() showInputButtons: boolean = false;
  @Input() textAreaMaxRows: number | null = null;
  @Input() textAreaRows: number | null = null;
  @Input() textAreaAllowScroll: boolean = false;

  @Output() onEnterPressed = new EventEmitter<void>();
  @Output() onEscapePressed = new EventEmitter<void>();
  @Output() valueChange = new EventEmitter<any>();
  @Output() checkButtonClicked = new EventEmitter<void>();
  @Output() crossButtonClicked = new EventEmitter<void>();
  @Output() focusin = new EventEmitter<FocusEvent>();
  @Output() focusout = new EventEmitter<FocusEvent>();


  @Input() isTooltip: boolean = false;
  @Input() tooltipText: string = '';
  @Input() tooltipIcon: string = '';
  private valueChangesSubscription: Subscription;

  @Output() contentOverflow = new EventEmitter<boolean>();

  @ViewChild('inputField') inputField: ElementRef;
  @ViewChild('textAreaField') textAreaField: ElementRef;

  // Define default validation messages
  defaultValidationMessages = {
    'required': 'errors.required',
    'email': 'errors.email',
    'minlength': 'errors.minlength',
    'maxlength': 'errors.maxlength',
    'min': 'errors.min',
    'max': 'errors.max',
    'pattern': 'errors.pattern'
  };


  constructor(private translate: TranslateService) {
  }

  ngOnInit() {
    this.valueChangesSubscription = this.control.valueChanges.subscribe(value => {
      if (!this.emitChangeOnBlurOnly) {
        this.valueChange.emit(value);
      }
      if (this.textArea && this.textAreaMaxRows !== null) {
        this.checkContentOverflow();
      }
    });

    // Merge default and input validation messages
    this.validationMessages = {...this.defaultValidationMessages, ...this.validationMessages};

    if (this.controlClassName) {
      switch (this.controlClassName) {
        case 'phone':
          this.setControlClassPhone();
          break;
        case 'first-name':
          this.setControlClassFirstName();
          break;
        case 'last-name':
          this.setControlClassLastName();
          break;
        case 'email':
          this.setControlClassEmail();
          break;
        case 'name':
          this.setControlClassName();
          break;
        case 'organisation-number':
          this.setControlClassOrganisationNumber();
          break;
        case 'address':
          this.setControlClassAddress();
          break;
        case 'zip-code':
          this.setControlClassZipCode();
          break;
        case 'city':
          this.setControlClassCity();
          break;
      }
    }
  }

  ngAfterViewInit() {
    if (this.takeFocus) {
      if (this.textArea) {
        this.textAreaField.nativeElement.focus();
      } else {
        this.inputField.nativeElement.focus();
      }
    }
    if (this.textArea && this.textAreaMaxRows !== null) {
      this.checkContentOverflow();
    }
  }

  get isRequired(): boolean {
    return this.isRequiredField(this.control);
  }

  onKeyDownEvent(event: KeyboardEvent): void {
    if (event.key === 'Enter') {
      if (!this.textArea) {
        this.inputField.nativeElement.blur();
      }
      this.onEnterPressed.emit();
    } else if (event.key === 'Escape') {
      event.stopPropagation();
      this.onEscapePressed.emit();
    }
  }


  getErrorMessage(): string {
    if (!this.control.errors || !this.control.touched) {
      return '';
    }

    const errorKey = Object.keys(this.control.errors)[0];
    const error = this.control.getError(errorKey);

    // Check if specific error message needs dynamic values
    switch (errorKey) {
      case 'minlength':
      case 'maxlength':
      case 'min':
      case 'max':
        // Use error.requiredLength, error.min, or error.max based on the validator
        return this.translate.instant(this.validationMessages[errorKey], {length: error.requiredLength || error.min || error.max});
      default:
        return this.translate.instant(this.validationMessages[errorKey]);
    }
  }

  isRequiredField(control: FormControl): boolean {
    if (control && control.validator) {
      const validatorResponse = control.validator(new FormControl(''));
      return !!validatorResponse && validatorResponse.hasOwnProperty('required');
    }
    return false;
  }

  ngOnDestroy(): void {
    if (this.valueChangesSubscription) {
      this.valueChangesSubscription.unsubscribe();
    }
  }

  setControlClassPhone() {
    this.inputPrefix = '+47';
    this.control.addValidators([
      Validators.pattern("^[0-9]*$"),
      Validators.minLength(8),
      Validators.maxLength(8)
    ])
    this.control.updateValueAndValidity()
  }

  setControlClassFirstName() {
    this.control.addValidators([
      Validators.pattern("^[a-zA-ZæøåÆØÅ\\-\\s]*$"),
      Validators.minLength(2),
      Validators.maxLength(50)
    ])
    this.control.updateValueAndValidity()
  }

  setControlClassLastName() {
    this.control.addValidators([
      Validators.pattern("^[a-zA-ZæøåÆØÅ\\-\\s]*$"),
      Validators.minLength(2),
      Validators.maxLength(50)
    ])
    this.control.updateValueAndValidity()
  }

  setControlClassName() {
    this.control.addValidators([
      Validators.pattern("^[a-zA-ZæøåÆØÅ\\-\\s]*$"),
      Validators.minLength(2),
      Validators.maxLength(200)
    ])
    this.control.updateValueAndValidity()
  }

  setControlClassEmail() {
    this.control.addValidators([
      Validators.email,
      Validators.minLength(5),
      Validators.maxLength(100)
    ])
    this.control.updateValueAndValidity()
  }


  setControlClassOrganisationNumber() {
    this.control.addValidators([
      Validators.pattern("^[0-9]*$"),
      Validators.minLength(9),
      Validators.maxLength(9)
    ])
    this.control.updateValueAndValidity()
  }

  setControlClassAddress() {
    this.control.addValidators([
      Validators.pattern("^[a-zA-ZæøåÆØÅ0-9\\-\\s]*$"),
      Validators.minLength(2),
      Validators.maxLength(100)
    ])
    this.control.updateValueAndValidity()
  }

  setControlClassZipCode() {
    this.control.addValidators([
      Validators.pattern("^[0-9]*$"),
      Validators.minLength(4),
      Validators.maxLength(4)
    ])
    this.control.updateValueAndValidity()
  }

  setControlClassCity() {
    this.control.addValidators([
      Validators.pattern("^[a-zA-ZæøåÆØÅ\\-\\s]*$"),
      Validators.minLength(2),
      Validators.maxLength(100)
    ])
    this.control.updateValueAndValidity()
  }

  preventDecimalInput(event: KeyboardEvent): void {
    if (this.hideDecimals && event.key === '.') {
      event.preventDefault();
    }
  }

  onBlur() {
    if(this.emitChangeOnBlurOnly) {
      this.valueChange.emit(this.control.value);
    }
  }

  sanitizeInput(event: any): void {
    if (this.hideDecimals) {
      const sanitizedValue = event.target.value.replace(/[^0-9]/g, '');
      event.target.value = sanitizedValue;
      // Assuming you have a form control named 'control' in this shared component
      if (this.control) {
        this.control.setValue(sanitizedValue, {emitEvent: false});
      }
    } else if (this.maxDecimals !== null) {
      const value = event.target.value;
      const splitValue = value.split('.');
      if (splitValue.length > 1) {
        const decimals = splitValue[1];
        if (decimals.length > this.maxDecimals) {
          const newValue = splitValue[0] + '.' + decimals.substring(0, this.maxDecimals);
          event.target.value = newValue;
          if (this.control) {
            this.control.setValue(newValue, {emitEvent: false});
          }
        }
      }

    }
  }

  onFocusIn(event: any) {
    this.focusin.emit(event);
  }

  onFocusOut(event: any) {
    this.focusout.emit(event);
  }

  checkContentOverflow() {
    this.contentOverflow.emit(this.textAreaField.nativeElement.scrollHeight > this.textAreaField.nativeElement.clientHeight);
  }

// Prevent the default scroll behavior
  preventScroll(event: WheelEvent): void {
    if (this.textAreaAllowScroll) {
      return;
    } else {
      event.preventDefault();
    }
  }

}

