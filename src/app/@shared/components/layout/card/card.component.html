<!--<div *ngIf="!hideComponent" [ngClass]="['profile-card', margin, colHeight]" [ngStyle]="{'background-color': backgroundColor, 'max-width': maxWidth}" (click)="$event.stopPropagation()">-->
<!--  <div class="profile-header-content">-->
<!--    <div class="profile-header">-->
<!--      <label class="profile-title">{{ labelKey | translate }} {{additionalTitleText}}</label>-->
<!--    <div class="profile-header justify-content-between d-flex align-items-center">-->
<!--      <div *ngIf="buttonHeader">-->
<!--        <ng-content select="[buttonHeader]"></ng-content>-->
<!--      </div>-->
<!--    </div>-->
<!--  </div>-->
<!--  <div class="profile-body" [ngStyle]="{ 'padding': padding }">-->
<!--    <ng-content select="[cardcontent]"></ng-content>-->
<!--  </div>-->
<!--</div>-->
<!--</div>-->


<div *ngIf="!hideComponent" [ngClass]="['profile-card', margin, colHeight, hideOverflow ? 'overflow-hidden' : '']" [ngStyle]="{'background-color': backgroundColor, 'max-width': maxWidth }">
  <div class="profile-header-content">
    <div class="profile-header justify-content-between d-flex align-items-center" *ngIf="header">
      <div class="d-flex align-items-center">
        <label *ngIf="!additionalTitleText" class="profile-title">{{ labelKey | translate }}</label>
        <div *ngIf="additionalTitleElement">
          <ng-content select="[additionalTitleElement]"></ng-content>
        </div>
        <label *ngIf="additionalTitleText" class="profile-title">{{ labelKey | translate }} {{additionalTitleText | translate}}</label>
      </div>
      <div *ngIf="buttonHeader">
        <ng-content select="[buttonHeader]"></ng-content>
      </div>
    </div>
  </div>
  <div class="profile-body" [ngStyle]="{ 'padding': padding}">
    <ng-content select="[cardcontent]"></ng-content>
  </div>
</div>
