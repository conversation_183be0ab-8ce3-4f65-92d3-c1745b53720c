.collapsed-subitem {
  background-color: #fafbfe !important;
}

.collapsed-subitem:hover {
  transition: 0s !important;
  background-color: #f1f2f5 !important;
}

.side-nav-item.active a {
  /*background-color: white;*/
  color: #448C74;
  border-radius: 60px;
  /*margin-left: 10px;*/
  font-weight: bold;
}

 /*Hover effect for primary navigation items */
/*.side-nav-item > a:hover {*/
  /*border-radius: 60px;*/
  /*background-color: white;*/
  /*margin-left: 10px;*/
/*}*/

 /*Hover effect for secondary navigation items */
/*.side-nav-second-level li a:hover {*/
/*  border-radius: 60px;*/
/*  margin-left: 10px;*/
/*}*/

.leftside-menu {
  position: fixed !important;
  overflow: scroll;
  overflow-x: visible;
  -ms-overflow-style: none;  /* Hide scrollbar in IE and Edge */
  scrollbar-width: none;  /* Hide scrollbar in Firefox */
}

.scrollable-menu {
  top: 124px;
  bottom: 0;
  z-index: 1000;
  position: fixed !important;
  overflow-y: scroll;
  -ms-overflow-style: none;  /* Hide scrollbar in IE and Edge */
  scrollbar-width: none;  /* Hide scrollbar in Firefox */
}

.scrollable-menu::-webkit-scrollbar {
  display: none;
}

.lsm-div {
  min-width: 260px;
  position: fixed;
  top: 0;
  bottom: 0;
  z-index: 1000;
  background-color: var(--ct-bg-leftbar);
  box-shadow: var(--ct-box-shadow);

}



/* Hide scrollbar for Chrome, Safari and Opera */
.leftside-menu::-webkit-scrollbar {
  display: none;
}

.side-nav-second-level li.active a {
  color: #448C74;
  /*margin-left: 10px;*/
  font-weight: bold;
}


.logo-toggle-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
}


.chevron-centered {
  margin: 0 auto;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}


.logo-toggle-container a {
  margin-right: 10px;
}

.fa-regular {
  font-size: 18px;
  cursor: pointer;
  margin-left: auto;
}

.side-nav-item {

}

.side-nav-item:hover {
    background-color: #f1f2f5;
}

.side-bar-button:hover {
  background-color: #f1f2f5;
}

.side-bar-list-item.condensed {
  width: 70px;
}

.side-bar-icon {
  margin: 0 20px 0 25px;
}

.side-bar-chevron {
  display: flex;
  justify-content: center;
  align-items: center;
  transform-origin: center;
  transition: transform 0.3s ease;

  font-size: 14px;
  margin-right: 20px;
  padding: 5px 0;
}

.side-bar-list-item {
  width: 260px;
  height: 54px;
  display: flex;
  padding: 17px 0;
  cursor: pointer;
  font-size: 15px;
}

.side-bar-list-item:hover {
  background-color: #f1f2f5;
}

.side-bar-list-item.active {
  font-weight: 700;
  color: #448C74;
}

.side-bar-dropdown {
}

.side-bar-dropdown .collapse.show {
  background-color: #f5f5f5;
}

.side-bar-chevron.dropdown-open {
  transform: rotate(90deg);
}

.side-bar-sub-list {
  background-color: white;
  padding-left: 0;
}

.side-bar-sub-list-item {
  display: flex;
  align-items: center;
  padding: 0 0 0 60px;
  height: 40px;
  background-color: white;
}


.side-bar-name {
}

.side-bar-name.active {
  font-weight: 700 !important;
}

/* Navigation link styles */
.side-bar-list-item a {
  color: inherit;
  text-decoration: none;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  padding: 17px 0;
  position: absolute;
  top: 0;
  left: 0;
}

.side-bar-list-item a:hover {
  color: inherit;
  text-decoration: none;
}

.side-bar-list-item a:visited {
  color: inherit;
}

.side-bar-list-item a:focus {
  color: inherit;
  text-decoration: none;
}

/* Remove padding from list item since anchor handles it now */
.side-bar-list-item-with-link {
  padding: 0;
  position: relative;
}

.side-bar-sub-list-item a {
  color: inherit;
  text-decoration: none;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  padding: 0 0 0 60px;
  position: absolute;
  top: 0;
  left: 0;
}

.side-bar-sub-list-item a:hover {
  color: inherit;
  text-decoration: none;
}

.side-bar-sub-list-item a:visited {
  color: inherit;
}

.side-bar-sub-list-item a:focus {
  color: inherit;
  text-decoration: none;
}

/* Remove padding from sub-list item since anchor handles it now */
.side-bar-sub-list-item-with-link {
  padding: 0;
  position: relative;
}
