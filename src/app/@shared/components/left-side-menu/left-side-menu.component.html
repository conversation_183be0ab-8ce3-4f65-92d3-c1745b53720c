
<!-- ========== Left Sidebar Start ========== -->
<div class="lsm-div">
<!--<div class="leftside-menu">-->
  <!-- Logo and Toggle Container -->
  <div class="logo-toggle-container">
    <!-- Full Logo (Only displayed when sidebar is not condensed) -->
    <a href="/dashboard" class="logo">
      <span class="logo-lg">
        <img src="assets/images/logo/Logo_positive.svg" alt="logo" width="119.8" height="22" />
      </span>
      <span class="logo-sm">
        <img src="assets/images/logo/Symbol_positive.svg" alt="small logo" width="35" height="22" />
      </span>
    </a>

    <!-- Sidebar Toggle Icon -->
    <i
      class="fa-regular"
      [ngClass]="isCondensed ? 'fa-chevrons-right chevron-centered my-3' : 'fa-chevrons-left me-3'"
      (click)="collapseLeftSideMenu()"
      *ngIf="showToggleButton"
      style="cursor: pointer; margin-left: auto;">
    </i>
  </div>

  <!--   Create list   -->
  <div class="side-bar-list-item position-relative border-bottom"
    (click)="toggleCreateList(!showCreateList)"
    (mouseenter)="toggleCreateList(true)"
    (mouseleave)="toggleCreateList(false)"
  >
    <i class="side-bar-icon fa-regular fa-circle-plus"></i>
    <div class="">{{ "leftSideNav.navItem.create.itemName" | translate }}</div>

    <div *ngIf="showCreateList" class="bg-white d-flex align-items-center shadow-sm position-absolute" style="left: 100%; top: -1px; border-radius: 10px; border: solid 1px #ededed; z-index: 9999">
      <!-- Job -->
      <div class="d-flex flex-column justify-content-center side-nav-item" style="height: 54px; width: 75px;" (click)="openCreateOrderModal()">
        <div>
          <div class="d-flex justify-content-center">
            <i class="fa-solid fa-hammer"></i>
          </div>
          <div class="text-center">Ordre</div>
        </div>
      </div>
      <!-- Employee -->
      <div class="d-flex flex-column justify-content-center side-nav-item" style="height: 54px;" (click)="addEmployee()">
        <div class="d-flex justify-content-center" style="width: 75px;">
          <i class="fa-solid fa-user"></i>
        </div>
        <div class="text-center">Ansatt</div>
      </div>
     <!-- Customer -->
      <div class="d-flex flex-column justify-content-center side-nav-item" style="height: 54px;" (click)="addCustomer()">
        <div class="d-flex justify-content-center" style="width: 75px; height: 14px; margin-bottom: 2px;">
          <span><i class="fa-regular fa-building-user"></i></span>
        </div>
        <div class="text-center">Kunde</div>
      </div>
    </div>

  </div>

  <!--- Side menu -->
  <ul *ngIf="applicationsLoaded" class="p-0 scrollable-menu pb-3">

    <!--   Dashboard   -->
    <li class="side-bar-list-item" [routerLinkActive]="['active']" [routerLinkActiveOptions]="{exact: false}">
      <a routerLink="/dashboard" class="d-flex align-items-center text-decoration-none">
        <i class="side-bar-icon fa-regular fa-house"></i>
        <div>{{ "leftSideNav.navItem.dashboard.itemName" | translate }}</div>
      </a>
    </li>

    <!--   Calendar   -->
    <li class="side-bar-list-item" [routerLinkActive]="['active']" [routerLinkActiveOptions]="{exact: false}">
      <a routerLink="/resource-calendar/v2" class="d-flex align-items-center text-decoration-none">
        <i class="side-bar-icon fa-regular fa-calendar"></i>
        <div>{{ "leftSideNav.navItem.resourceCalendar.itemName" | translate }}</div>
      </a>
    </li>

    <hr class="my-{{hrMargin}}">

    <!--   Orders   -->
    <li class="side-bar-list-item" [routerLinkActive]="['active']" [routerLinkActiveOptions]="{exact: false}">
      <a routerLink="/orders" class="d-flex align-items-center text-decoration-none">
        <i class="side-bar-icon fa-regular fa-list"></i>
        <div>{{ "leftSideNav.navItem.orders.itemName" | translate }}</div>
      </a>
    </li>

    <!--  Work Orders   -->
    <li class="side-bar-list-item" [routerLinkActive]="['active']" [routerLinkActiveOptions]="{exact: false}">
      <a routerLink="/work-orders" class="d-flex align-items-center text-decoration-none">
        <i class="side-bar-icon fa-regular fa-hammer"></i>
        <div>{{ "leftSideNav.navItem.workOrders.itemName" | translate }}</div>
      </a>
    </li>

    <!--   Payments   -->
    <li class="side-bar-list-item" [routerLinkActive]="['active']" [routerLinkActiveOptions]="{exact: false}">
      <a routerLink="/payments" class="d-flex align-items-center text-decoration-none">
        <i class="side-bar-icon fa-regular fa-money-check-dollar"></i>
        <div>{{ "leftSideNav.navItem.payments.itemName" | translate }}</div>
      </a>
    </li>

    <hr class="my-{{hrMargin}}">

    <!--   Products   -->
    <li class="side-bar-list-item" [routerLinkActive]="['active']" [routerLinkActiveOptions]="{exact: false}">
      <a routerLink="/products" class="d-flex align-items-center text-decoration-none">
        <i class="side-bar-icon fa-regular fa-tag fa-flip-horizontal"></i>
        <div>{{ "leftSideNav.navItem.products.itemName" | translate }}</div>
      </a>
    </li>

    <!-- Templates moved to settings
    <li class="side-bar-list-item side-bar-dropdown" data-bs-toggle="collapse" data-bs-target="#sidebarTemplates" (click)="toggleDropDown('sidebarTemplates')">
      <i class="side-bar-icon fa-regular fa-memo-pad"></i>
      <div>{{ "leftSideNav.navItem.templates.itemName" | translate }}</div>
      <i class="fa-regular fa-chevron-right side-bar-chevron" [ngClass]="{'dropdown-open': openDropDowns.includes('sidebarTemplates')}"></i>
    </li>

    <div class="collapse" id="sidebarTemplates">
        <ul class="side-bar-sub-list">
          <li class="side-bar-list-item side-bar-sub-list-item" [routerLinkActive]="['active']" [routerLinkActiveOptions]="{exact: false}" routerLink="/templates/work-orders">
            <div>{{ "leftSideNav.navItem.templates.workOrders" | translate }}</div>
          </li>

          <li class="side-bar-list-item side-bar-sub-list-item" [routerLinkActive]="['active']" [routerLinkActiveOptions]="{exact: false}" routerLink="/templates/tasks">
            <div>{{ "leftSideNav.navItem.templates.tasks" | translate }}</div>
          </li>

          <li class="side-bar-list-item side-bar-sub-list-item" [routerLinkActive]="['active']" [routerLinkActiveOptions]="{exact: false}" routerLink="/templates/customer-questions">
            <div>{{ "leftSideNav.navItem.templates.customerQuestions" | translate }}</div>
          </li>
        </ul>
      </div>
    -->

    <!--   Contacts   -->
    <li class="side-bar-list-item side-bar-dropdown" data-bs-toggle="collapse" data-bs-target="#sidebarContacts" (click)="toggleDropDown('sidebarContacts')">
      <i class="side-bar-icon fa-regular fa-user-group" style="margin-right: 13px;"></i>
      <div>{{ "leftSideNav.navItem.contacts.itemName" | translate }}</div>
      <i class="fa-regular fa-chevron-right side-bar-chevron" [ngClass]="{'dropdown-open': openDropDowns.includes('sidebarContacts')}"></i>
    </li>

    <div class="collapse" id="sidebarContacts">
      <ul class="side-bar-sub-list">
        <!--   Business contacts   -->
        <li class="side-bar-list-item side-bar-sub-list-item" [routerLinkActive]="['active']" [routerLinkActiveOptions]="{exact: false}">
          <a routerLink="/affiliates/business" class="d-flex align-items-center text-decoration-none">
            <div>{{ "leftSideNav.navItem.businesses.itemName" | translate }}</div>
          </a>
        </li>

        <!--   Private contacts   -->
        <li class="side-bar-list-item side-bar-sub-list-item" [routerLinkActive]="['active']" [routerLinkActiveOptions]="{exact: false}">
          <a routerLink="/affiliates/private" class="d-flex align-items-center text-decoration-none">
            <div>{{ "leftSideNav.navItem.customers.privateCustomers" | translate }}</div>
          </a>
        </li>
      </ul>
    </div>


    <!-- Employees moved to settings
    <li class="side-bar-list-item" [routerLinkActive]="['active']" [routerLinkActiveOptions]="{exact: false}" routerLink="/employees/">
      <i class="side-bar-icon fa-regular fa-user"></i>
      <div>{{ "leftSideNav.navItem.employees.itemName" | translate }}</div>
    </li>
    -->

    <!-- Resources moved to settings
    <li *ngIf="resourcesEnabled" class="side-bar-list-item" [routerLinkActive]="['active']" [routerLinkActiveOptions]="{exact: false}" routerLink="/resources/">
      <i class="side-bar-icon fa-regular fa-truck" style="margin-right: 13px;"></i>
      <div>{{ "leftSideNav.navItem.resources.itemName" | translate }}</div>
    </li>
    -->

    <hr class="my-{{hrMargin}}">

    <!--  Received Work Orders   -->
    <li class="side-bar-list-item" [routerLinkActive]="['active']" [routerLinkActiveOptions]="{exact: true}">
      <a routerLink="/work-orders/received" class="d-flex align-items-center text-decoration-none">
        <i class="side-bar-icon fa-regular fa-arrow-down-to-bracket"></i>
        <div>{{ "leftSideNav.navItem.receivedWorkOrders.itemName" | translate }}<span *ngIf="unansweredJobs !== 0" class="ms-1">({{unansweredJobs}})</span></div>
      </a>
    </li>

    <!--   Reports   -->
    <li class="side-bar-list-item side-bar-dropdown" data-bs-toggle="collapse" data-bs-target="#sidebarReports" (click)="toggleDropDown('sidebarReports')">
      <i class="side-bar-icon fa-regular fa-file-chart-column"></i>
      <div>{{ "leftSideNav.navItem.reports.itemName" | translate }}</div>
      <i class="fa-regular fa-chevron-right side-bar-chevron" [ngClass]="{'dropdown-open': openDropDowns.includes('sidebarReports')}"></i>
    </li>

    <div class="collapse" id="sidebarReports">
      <ul class="side-bar-sub-list">
        <!--   Financial   -->
        <li class="side-bar-list-item side-bar-sub-list-item" [routerLinkActive]="['active']" [routerLinkActiveOptions]="{exact: false}">
          <a routerLink="/reports/financial" class="d-flex align-items-center text-decoration-none">
            <div>{{ "leftSideNav.navItem.reports.financialReports" | translate }}</div>
          </a>
        </li>

        <!--   Time tracking   -->
        <li class="side-bar-list-item side-bar-sub-list-item" [routerLinkActive]="['active']" [routerLinkActiveOptions]="{exact: false}">
          <a routerLink="/reports/timetracking" class="d-flex align-items-center text-decoration-none">
            <div>{{ "leftSideNav.navItem.reports.employeeTimeTracking" | translate }}</div>
          </a>
        </li>

        <!--   Employee Time Tracking   -->
        <li class="side-bar-list-item side-bar-sub-list-item" [routerLinkActive]="['active']" [routerLinkActiveOptions]="{exact: false}">
          <a routerLink="/reports/employee-timetracking" class="d-flex align-items-center text-decoration-none">
            <div>{{ "leftSideNav.navItem.reports.employeeReports" | translate }}</div>
          </a>
        </li>


        <!--   Order Reports   -->
<!--        <li class="side-bar-list-item side-bar-sub-list-item" [routerLinkActive]="['active']" [routerLinkActiveOptions]="{exact: false}" routerLink="/reports/order-reports">-->
<!--          <div>{{ "leftSideNav.navItem.reports.orderReports" | translate }}</div>-->
<!--        </li>-->
      </ul>
    </div>

    <!--   Salary   -->
    <li *ngIf="timeTrackingEnabled"  class="side-bar-list-item side-bar-dropdown" data-bs-toggle="collapse" data-bs-target="#sidebarSalary" (click)="toggleDropDown('sidebarSalary')">
      <i class="side-bar-icon fa-regular fa-money-check-pen"></i>
      <div>{{ "leftSideNav.navItem.salary.itemName" | translate }}</div>
      <i class="fa-regular fa-chevron-right side-bar-chevron" [ngClass]="{'dropdown-open': openDropDowns.includes('sidebarSalary')}"></i>
    </li>

    <div class="collapse" id="sidebarSalary">
      <ul class="side-bar-sub-list">

        <!--   Salary   -->
        <li class="side-bar-list-item side-bar-sub-list-item" [routerLinkActive]="['active']" [routerLinkActiveOptions]="{exact: false}">
          <a routerLink="/salary/overview" class="d-flex align-items-center text-decoration-none">
            <div>{{ "leftSideNav.navItem.salary.approval" | translate }}</div>
          </a>
        </li>

        <!--   Approved hours   -->
        <li class="side-bar-list-item side-bar-sub-list-item" [routerLinkActive]="['active']" [routerLinkActiveOptions]="{exact: false}">
          <a routerLink="/salary/approved-hours" class="d-flex align-items-center text-decoration-none">
            <div>{{ "salary.approvedHours.title" | translate }}</div>
          </a>
        </li>

        <!--   Salary approvals   -->
        <li class="side-bar-list-item side-bar-sub-list-item" [routerLinkActive]="['active']" [routerLinkActiveOptions]="{exact: false}">
          <a routerLink="/salary/approvals" class="d-flex align-items-center text-decoration-none">
            <div>{{ "salary.approval.approvals.title" | translate }}</div>
          </a>
        </li>

        <!--   Absence   -->
        <li class="side-bar-list-item side-bar-sub-list-item" [routerLinkActive]="['active']" [routerLinkActiveOptions]="{exact: false}">
          <a routerLink="/salary/absence" class="d-flex align-items-center text-decoration-none">
            <div>{{ "salary.absence.title" | translate }}</div>
          </a>
        </li>

      </ul>
    </div>

    <!--   Reportinator   -->
    <li *ngIf="reportinatorEnabled" class="side-bar-list-item side-bar-dropdown" data-bs-toggle="collapse" data-bs-target="#sidebarReportinator" (click)="toggleDropDown('sidebarReportinator')">
      <i class="side-bar-icon fa-regular fa-pen-ruler" style="margin-right: 17px;"></i>
      <div>{{ "Taksering" | translate }}</div>
      <i class="fa-regular fa-chevron-right side-bar-chevron" [ngClass]="{'dropdown-open': openDropDowns.includes('sidebarReportinator')}"></i>
    </li>

    <div class="collapse" id="sidebarReportinator">
      <ul class="side-bar-sub-list">
        <!--   Rapporter   -->
        <li class="side-bar-list-item side-bar-sub-list-item" [routerLinkActive]="['active']" [routerLinkActiveOptions]="{exact: false}">
          <a routerLink="/reportinator/reports" class="d-flex align-items-center text-decoration-none">
            <div>{{ "Rapporter" | translate }}</div>
          </a>
        </li>

        <!--   Maler   -->
        <li class="side-bar-list-item side-bar-sub-list-item" [routerLinkActive]="['active']" [routerLinkActiveOptions]="{exact: false}">
          <a routerLink="/reportinator/templates" class="d-flex align-items-center text-decoration-none">
            <div>{{ "Maler" | translate }}</div>
          </a>
        </li>

        <!--   Elementer   -->
        <li class="side-bar-list-item side-bar-sub-list-item" [routerLinkActive]="['active']" [routerLinkActiveOptions]="{exact: false}">
          <a routerLink="/reportinator/elements" class="d-flex align-items-center text-decoration-none">
            <div>{{ "Elementer" | translate }}</div>
          </a>
        </li>
      </ul>
    </div>

    <!--   Settings   -->
    <li class="side-bar-list-item" [routerLinkActive]="['active']" [routerLinkActiveOptions]="{exact: false}">
      <a routerLink="/settings" class="d-flex align-items-center text-decoration-none">
        <i class="side-bar-icon fa-regular fa-gear" style="margin-right: 17px;"></i>
        <div>{{ "leftSideNav.navItem.settings.itemName" | translate }}</div>
      </a>
    </li>

    <!--   Super admin   -->
    <li *ngIf="superAdminAccess" class="side-bar-list-item side-bar-dropdown" data-bs-toggle="collapse" data-bs-target="#sidebarSuperAdmin" (click)="toggleDropDown('sidebarSuperAdmin')">
      <i class="side-bar-icon fa-regular fa-gear" style="margin-right: 17px;"></i>
      <div>{{ "leftSideNav.navItem.superadmin.itemName" | translate }}</div>
    </li>

    <div class="collapse" id="sidebarSuperAdmin">
      <ul class="side-bar-sub-list">
        <!--   Company   -->
        <li class="side-bar-list-item side-bar-sub-list-item" [routerLinkActive]="['active']" [routerLinkActiveOptions]="{exact: false}">
          <a routerLink="/superadmin/companies" class="d-flex align-items-center text-decoration-none">
            <div>{{ "leftSideNav.navItem.superadmin.companies" | translate }}</div>
          </a>
        </li>

        <!--   Top bar setup   -->
        <li class="side-bar-list-item side-bar-sub-list-item" [routerLinkActive]="['active']" [routerLinkActiveOptions]="{exact: false}">
          <a routerLink="/superadmin/top-bar-setup" class="d-flex align-items-center text-decoration-none">
            <div>{{ "Top bar setup" | translate }}</div>
          </a>
        </li>
      </ul>
    </div>

  </ul>

  <!--- End Sidemenu -->
</div>

<!-- ========== Left Sidebar End ========== -->
