import {Component, HostListener, OnInit, Renderer2} from '@angular/core';
import {AuthService} from "../../../@core/services/auth.service";
import {StorageService} from "../../../@core/services/storage.service";
import {NavigationEnd, Router, RouterLink, RouterLinkActive} from "@angular/router";
import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {AddEmployeeComponent} from "../../../pages/employees/employees-overview/_modals/add-employee/add-employee.component";
import {NewCustomerModalComponent} from "../new-customer-modal/new-customer-modal.component";
import {CustomerResponse} from "../../models/customer.interfaces";
import {_CRM_ORD_168} from "../../models/input.interfaces";
import {takeUntil} from "rxjs/operators";
import {OrderService} from "../../services/order.service";
import { CompanyService } from '../../services/company.service';
import {OverlayService} from "../../services/overlay.service";
import {StandardImports} from "../../global_import";

@Component({
    selector: 'app-left-side-menu',
    templateUrl: './left-side-menu.component.html',
    styleUrls: ['./left-side-menu.component.css'],
    standalone: true,
  imports: [StandardImports, RouterLinkActive, RouterLink]
})
export class LeftSideMenuComponent implements OnInit {
  showToggleButton: boolean = true;
  superAdminAccess: boolean = false;
  applicationsLoaded = false;
  repeatingOrdersEnabled = false;
  accountingEnabled = false;
  estimateCalculationEnabled = false;
  resourcesEnabled = false;
  timeTrackingEnabled = false;
  reportinatorEnabled = false;
  crewUrl = 'https://app.between.as';
  isCondensed: boolean = false;
  showCreateList: boolean = false;
  hrMargin = 0;
  sideBarWidthPx = 260;
  openDropDowns: string[] = [];
  leftSideMenuCollapsed: boolean = false;
  mobileView: boolean = false;
  unansweredJobs: number = 0;

  constructor(private authService: AuthService, private overlayService: OverlayService, protected storageService: StorageService, private router: Router, private renderer: Renderer2, private modalService: NgbModal, private orderService: OrderService, private companyService: CompanyService) {
    this.router.events.subscribe(() => {
      this.overlayService.minimizeAllOverlays();
    });
  }

  ngOnInit(): void {
    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.hideMenuOnMobile();
      }
    });
   this.storageService.contractorWorkOrders$.subscribe((res) => {
      this.unansweredJobs = res;
    });

    // Fetch these to have them ready for the create order modal
    this.storageService.registerWorkOrderTemplates();

    this.storageService.registerContractorWorkOrders();

    this.storageService.leftSideMenuCollapsed$.subscribe((data) => {
      this.leftSideMenuCollapsed = data;
    });

    this.storageService.mobileView$.subscribe((data) => {
      this.mobileView = data;
    });

    this.storageService.applicationsLoaded$.subscribe((data) => {
      this.applicationsLoaded = data;
    });

    this.storageService.accountingEnabled$.subscribe((data) => {
      this.accountingEnabled = data;
    });

    this.storageService.resourcesEnabled$.subscribe((data) => {
      this.resourcesEnabled = data;
    });
    this.storageService.timeTrackingEnabled$.subscribe((data) => {
      this.timeTrackingEnabled = data;
    });

    this.storageService.reportinatorEnabled$.subscribe((data) => {
      this.reportinatorEnabled = data;
    });

    const htmlElement = document.documentElement;
    // this.isCondensed = htmlElement.getAttribute('data-sidenav-size') === 'condensed';
    this.superAdminAccess = this.authService.checkComponentAccess('SUP-0');

    // this.checkScreenWidth(); // Initial check

  }

  // Check screen size
  // @HostListener('window:resize', ['$event'])
  // onResize(event: any): void {
  //   this.checkScreenWidth();
  // }
  //
  // checkScreenWidth(): void {
  //   console.log('Checking screen width');
  //   const screenWidth = window.innerWidth;
  //
  //   this.showToggleButton = screenWidth >= 768;
  //
  //   if (screenWidth < 768) {
  //     this.expandSidebarMobile();
  //   }
  // }


  collapseLeftSideMenu() {
    this.storageService.setLeftSideMenuCollapsed(true);
  }


  // Method to collapse the sidebar (for larger screens)
  // expandSidebarMobile(): void {
  //   const htmlElement = document.documentElement;
  //   const menuElement = document.querySelector('.leftside-menu');
  //
  //   if (menuElement) {
  //     this.renderer.setAttribute(htmlElement, 'data-sidenav-size', 'default');
  //     menuElement.classList.remove('expanded');
  //     menuElement.classList.add('collapsed');
  //     this.isCondensed = false;
  //
  //     // Hide the sidebar completely, like mobile
  //     this.renderer.addClass(document.body, 'sidebar-hidden');
  //     this.renderer.removeClass(document.body, 'sidebar-enable');
  //   } else {
  //     console.error('Menu element not found');
  //   }
  // }



  // Method to expand the sidebar
  // expandSidebar(): void {
  //   const htmlElement = document.documentElement;
  //   const menuElement = document.querySelector('.leftside-menu');
  //
  //   if (menuElement) {
  //     this.renderer.setAttribute(htmlElement, 'data-sidenav-size', 'default');
  //     menuElement.classList.remove('collapsed');
  //     menuElement.classList.add('expanded');
  //     this.isCondensed = false;
  //     this.renderer.removeClass(document.body, 'sidebar-hidden');
  //     this.renderer.addClass(document.body, 'sidebar-enable');
  //   }
  // }

  // Method to hide the sidebar on mobile after navigation
  hideMenuOnMobile(): void {
    const screenWidth = window.innerWidth;
    if (screenWidth < 768) {
      // this.renderer.removeClass(document.body, 'sidebar-enable');
      // this.renderer.addClass(document.body, 'sidebar-hidden');
      this.storageService.setLeftSideMenuCollapsed(true);
    }
  }

  // Method to collapse the sidebar (for larger screens)
  // collapseSidebar(): void {
  //   const htmlElement = document.documentElement;
  //   const menuElement = document.querySelector('.leftside-menu');
  //
  //   if (menuElement) {
  //     this.renderer.setAttribute(htmlElement, 'data-sidenav-size', 'condensed');
  //     menuElement.classList.remove('expanded');
  //     menuElement.classList.add('collapsed');
  //     this.isCondensed = true;
  //
  //     // Hide the sidebar completely, like mobile
  //     this.renderer.addClass(document.body, 'sidebar-hidden');
  //     this.renderer.removeClass(document.body, 'sidebar-enable');
  //   } else {
  //     console.error('Menu element not found');
  //   }
  // }

  toggleSidebarSizeButtonClick() {
    this.storageService.setLeftSideMenuCollapsed(!this.isCondensed);
  }

  // Toggle between 'default' and 'condensed' for larger screens
  // toggleSidebarSize(): void {
  //   const htmlElement = document.documentElement;
  //   const currentSize = htmlElement.getAttribute('data-sidenav-size');
  //   const menuElement = document.querySelector('.leftside-menu');
  //
  //   if (menuElement) {
  //     if (!this.isCondensed) {
  //       this.expandSidebar();
  //     } else {
  //       this.collapseSidebar();
  //     }
  //   } else {
  //     console.error('Menu element not found');
  //   }
  // }

  goToLink(url: string): void {
    window.open(url, '_blank');
  }

  isRouteActive(route: string): boolean {
    return this.router.url === route;
  }

  async openCreateOrderModal() {
    const { WorkOrderDetailsComponent } = await import('../../../pages/work-orders/components/work-order-details/work-order-details.component');
    let modalRef = this.modalService.open(WorkOrderDetailsComponent);
    modalRef.componentInstance.viewSettings = {
      modalView: true,
      createView: true,
      workOrderView: true,
      createOrderView: true
    }
  }

  toggleCreateList(show: boolean) {
    this.showCreateList = show;
  }

  addEmployee(){
    const modalRef = this.modalService.open(AddEmployeeComponent, {size: 'lg'});
  }

  addCustomer() {
    const modalRef = this.modalService.open(NewCustomerModalComponent)
    modalRef.result.then((result: CustomerResponse | null) => {
      if (result) {
        if (result.is_private === 1) {
          this.router.navigate(['/customers/private/', result.affiliate_id]);
        } else {
          this.router.navigate(['/affiliates/details/', result.affiliate_id]);
        }
      }
    });
  }

  toggleDropDown(elementId: string) {
    if (this.openDropDowns.includes(elementId)) {
      this.openDropDowns = this.openDropDowns.filter((id) => id !== elementId);
    } else {
      this.openDropDowns.push(elementId);
    }
  }

  protected readonly document = document;
}
