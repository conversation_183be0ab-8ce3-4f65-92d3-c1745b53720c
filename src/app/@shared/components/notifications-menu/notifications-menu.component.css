/* Base panel styling */
.notifications-panel {
  position: fixed;
  top: 0;
  right: -400px; /* Adjusted width */
  width: 400px;
  height: 100%;
  background: #fff;
  box-shadow: -4px 0 16px rgba(0, 0, 0, 0.1);
  transition: right 0.3s ease;
  z-index: 1050;
  display: flex;
  flex-direction: column;
}

.notifications-panel.open {
  right: 0;
}

.notifications-header {
  display: flex;
  flex-direction: column;
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-bottom {
  margin-top: 10px; /* optional spacing */
}

.close-btn {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #888;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.mark-all-read-btn {
  background: none;
  border: none;
  color: var(--primary-color, #2196F3);
  font-size: 0.85rem;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
}

.mark-all-read-btn:hover {
  background-color: rgba(33, 150, 243, 0.1);
}

/* Content styling */
.notifications-content {
  flex: 1;
  overflow-y: auto;
}

/* Notification list styling */
.notification-list {
  list-style: none;
  margin: 0;
  padding: 0;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 15px;
  border-bottom: 1px solid #f0f0f0;
  transition: background 0.2s;
  cursor: pointer;
  position: relative;
}

.notification-item:hover {
  background: #f5f7fa;
}

.notification-icon {
  flex-shrink: 0;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: white;
}

.notification-icon.work-order {
  background-color: #4CAF50;
}

.notification-icon.order {
  background-color: #2196F3;
}

.notification-icon.payment {
  background-color: #FF9800;
}

.notification-icon.note {
  background-color: #9C27B0;
}

.notification-icon.rating {
  background-color: #F44336;
}

.notification-icon.generic {
  background-color: #607D8B;
}

.notification-details {
  flex: 1;
}

.notification-title {
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
  font-size: 0.95rem;
}

.notification-body {
  color: #5c6c7c;
  font-size: 0.9rem;
  margin-bottom: 8px;
  line-height: 1.4;
}

.notification-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.8rem;
  color: #8795a1;
}

.notification-meta-right {
  display: flex;
  gap: 8px;
}

.notification-time {
  color: #8795a1;
}

.notification-company {
  background-color: #fae4cc;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
}

.notification-category{
  background-color: #e9ecef;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
}

/* Empty state styling */
.empty-notifications {
  text-align: center;
  padding: 40px 20px;
  color: #888;
}

.empty-notifications i {
  font-size: 2.5rem;
  margin-bottom: 10px;
  display: block;
}

.loading-spinner {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px; /* Adjust height as needed to center vertically */
  width: 100%;
}

.spinner {
  border: 6px solid #f3f3f3; /* Light grey background */
  border-top: 6px solid #3498db; /* Blue color for the spinner's top */
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.notification-group-header {
  cursor: pointer;
  padding: 10px;
  background-color: #f5f5f5;
}

.notification-group-header:hover {
  cursor: pointer;
  padding: 10px;
  background-color: #e1e1e1;
}

.header-content {
  display: flex;
  justify-content: center;
  align-items: center;
}

.header-date {
  margin-right: 8px; /* adjust spacing as needed */
  font-size: 16px;
  font-weight: 500;
}

.chevron {
  transition: transform 0.3s ease;
  font-size: 24px;
}

/* Rotate chevron when group is open */
.chevron.open {
  transform: rotate(180deg);
}

.unread-indicator {
  position: absolute;
  top: 50%;
  right: 15px;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  background-color: var(--primary-color);
  border-radius: 50%;
}

.profile-content {
  border: 2px solid #DEE2E7;
  border-radius: 10px;
  background-color: white;
  padding: 0.5rem;
}

/* Remove the border for the switch */
.custom-switch .form-check-input {
  border: none !important;
  box-shadow: none !important;
}

/* Set the background color when the switch is off */
.custom-switch .form-check-input:not(:checked) {
  background-color: #ececec;
}

.custom-switch .form-check-input {
  border: none;
  box-shadow: none;
  transform: scale(1.5);
  margin: 10px;
  min-width: 32px; /* Set a minimum width to make the switch longer */
}

.additional-message {
  margin-top: 5px;
  padding-top: 5px;
  border-top: 1px dashed #e0e0e0;
  font-style: italic;
}

.additional-message p {
  margin: 0 10px 0 0;
}

/* Responsive adjustments for mobile devices */
@media (max-width: 576px) {
  .notifications-panel {
    width: 100%;
    right: -100%;
  }

  .notifications-panel.open {
    right: 0;
  }

  .notifications-header, .notifications-content {
    padding: 12px;
  }

  .notification-icon {
    width: 36px;
    height: 36px;
    font-size: 1.1rem;
  }
}
