<div class="notifications-panel" [ngClass]="{'open': isOpen}">
  <div class="notifications-header">
    <div class="header-top">
      <h4>{{ 'notifications.header' | translate }}</h4>
     <div class="header-actions">
       <app-spinner *ngIf="markAllAsReadLoading"></app-spinner>
        <button class="mark-all-read-btn" *ngIf="hasUnreadNotifications() && !markAllAsReadLoading" (click)="markAllAsRead()">
          {{ 'notifications.markAllAsRead' | translate }}
        </button>
        <button class="close-btn" (click)="closeMenu()">×</button>
      </div>
    </div>
<!--    <div class="notification-filter">-->
<!--      <label for="categoryFilter">Filter by Category:</label>-->
<!--      <select id="categoryFilter" [(ngModel)]="selectedCategoryId" (change)="applyCategoryFilter()">-->
<!--        <option [ngValue]="null">All</option>-->
<!--        <option *ngFor="let cat of notificationCategories" [ngValue]="cat.category_id">-->
<!--          {{ cat.loc_category_name.en }}-->
<!--        </option>-->
<!--      </select>-->
<!--    </div>-->
  </div>


  <div *ngIf="!isSettingsOpen" class="notifications-content" #notificationsContent (scroll)="onScroll($event)">
    <!-- Loop over grouped days -->
    <div *ngFor="let day of groupedDays">
      <!-- Group header with dropdown toggle -->
      <div class="notification-group-header" (click)="toggleGroup(day)">
        <div class="header-content">
          <span class="header-date">{{ day | date:'fullDate' }}</span>
          <i class="material-icons chevron">
            {{ isGroupOpen(day) ? 'expand_more' : 'expand_less' }}
          </i>
        </div>
      </div>


      <!-- List notifications for this day if group is open -->
      <div *ngIf="isGroupOpen(day)">
        <ul class="notification-list">
          <li class="notification-item" *ngFor="let item of groupedNotifications[day]" (click)="handleNotification(item)">
            <div class="notification-icon" [ngClass]="getNotificationIconClass(item.category_id)">
              <i [class]="getNotificationIcon(item.category_id)"></i>
            </div>
            <div class="notification-details">
              <div class="notification-title">{{ item.title }}</div>
              <div class="notification-body">
                {{ item.body }}
                <div *ngIf="item.category_id === 4 && item.additional_data?.message" class="additional-message">
                  <p>{{ (item.additional_data.message.length > 200) ? (item.additional_data.message | slice:0:200) + '...' : item.additional_data.message }}</p>
                </div>
              </div>
              <div class="notification-meta">
                <span class="notification-time">{{utilsService.getRelativeTime(item.set_at)}}</span>
                <div class="notification-meta-right">
                  <span class="notification-category">{{ getCategoryName(item.category_id) }}</span>
                  <span class="notification-company"
                        *ngIf="item.company_name && item.company_id !== currentCompanyId">
                    {{ item.company_name }}
                  </span>
                </div>
              </div>
            </div>
            <div class="unread-indicator" *ngIf="!item.read"></div>
          </li>
        </ul>
      </div>
    </div>

    <!-- Loading indicator -->
    <div class="loading-spinner" *ngIf="loading && notifications.length !== 0">
      <div class="spinner"></div>
    </div>

    <!-- Show More Button -->
    <div class="show-more" *ngIf="hasMore && !loading">
      <app-button [buttonType]="'link'" [translationKey]="'notifications.show_more'" (buttonClick)="onShowMore()"></app-button>
    </div>

    <!-- Empty State -->
    <div class="empty-notifications" *ngIf="!loading && notifications.length === 0">
      <i class="mdi mdi-bell-off-outline"></i>
      <p>{{ 'notifications.empty' | translate }}</p>
    </div>
  </div>
</div>
