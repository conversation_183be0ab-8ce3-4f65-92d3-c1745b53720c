import {Component, <PERSON>ementRef, OnInit, ViewChild} from '@angular/core';
import {StorageService} from "../../../@core/services/storage.service";
import {NotificationService} from "../../services/notification.service";
import {NotificationsResponse} from "../../models/notification";
import {TranslateService} from "@ngx-translate/core";
import {Router} from "@angular/router";
import {UtilsService} from "../../../@core/utils/utils.service";
import {FormBuilder, FormGroup} from "@angular/forms";
import {UserService} from "../../../@core/services/user.service";
import {StandardImports} from "../../global_import";
import {_CRM_NOT_1} from "../../models/input.interfaces";
import {SpinnerComponent} from "../spinner/spinner.component";
import {UserEntityRelationWithoutUserDataResponse} from "../../models/user.interfaces";
import {ToastService} from "../../../@core/services/toast.service";

@Component({
  selector: 'app-notifications-menu',
  templateUrl: './notifications-menu.component.html',
  styleUrls: ['./notifications-menu.component.css'],
  standalone: true,
  imports: [StandardImports, SpinnerComponent],
})
export class NotificationsMenuComponent implements OnInit {
  notifications: NotificationsResponse[] = [];
  groupedNotifications: { [day: string]: NotificationsResponse[] } = {};
  groupedDays: string[] = [];
  openGroups: { [day: string]: boolean } = {};
  isSettingsOpen: boolean = false;
  settingsForm: FormGroup;
  user_id: string = '';
  currentCompanyId: string = '';
  isOpen: boolean = false;
  page: number = 1;
  limit: number = 10;
  loading: boolean = false;
  hasMore: boolean = true;
  markAllAsReadLoading: boolean = false;
  companies: UserEntityRelationWithoutUserDataResponse[] = [];

  // NEW: Properties for category filtering
  selectedCategoryId: number | null = null;
  notificationCategories = [
    { category_id: 0, loc_category_name: { en: 'Generic', no: 'Generisk' } },
    { category_id: 1, loc_category_name: { en: 'Order', no: 'Ordre' } },
    { category_id: 2, loc_category_name: { en: 'Payment', no: 'Betalinger' } },
    { category_id: 3, loc_category_name: { en: 'Work order', no: 'Arbeidsordre' } },
    { category_id: 4, loc_category_name: { en: 'Note', no: 'Notat' } },
    { category_id: 5, loc_category_name: { en: 'Rating', no: 'Anmeldelse' } }
  ];

  @ViewChild('notificationsContent') notificationsContent!: ElementRef;

  constructor(
    private storageService: StorageService,
    private notificationService: NotificationService,
    private router: Router,
    private userService: UserService,
    private fb: FormBuilder,
    public utilsService: UtilsService,
    private translate: TranslateService,
    private toastService: ToastService
  ) {}

  ngOnInit(): void {
    // Initialize the settings form
    this.settingsForm = this.fb.group({
      ntf_internal_note: this.fb.group({ email: [false], sms: [false], core_push: [false], crew_push: [false] }),
      ntf_order_accepted: this.fb.group({ email: [false], sms: [false], core_push: [false], crew_push: [false] }),
      ntf_order_finished: this.fb.group({ email: [false], sms: [false], core_push: [false], crew_push: [false] }),
      ntf_upcoming_order_not_accepted: this.fb.group({ email: [false], sms: [false], core_push: [false], crew_push: [false] }),
      ntf_order_rating: this.fb.group({ email: [false], sms: [false], core_push: [false], crew_push: [false] }),
      ntf_embed_order: this.fb.group({ email: [false], sms: [false], core_push: [false], crew_push: [false] }),
      receive_customer_email_error_notification: this.fb.group({ email: [false], sms: [false], core_push: [false], crew_push: [false] }),
      ntf_new_customer_msg: this.fb.group({ email: [false], sms: [false], core_push: [false], crew_push: [false] }),
      ntf_sub_contractor_order: this.fb.group({ email: [false], sms: [false], core_push: [false], crew_push: [false] }),
    });

    this.currentCompanyId = this.storageService.getSelectedCompanyId();
    this.getUserCompanies();

    this.storageService.isMenuOpen$.subscribe((open) => {
      this.isOpen = open;
      if (open) {
        this.page = 1;
        this.notifications = [];
        this.groupedNotifications = {};
        this.groupedDays = [];
        this.openGroups = {};
        this.hasMore = true;
        this.loadNotifications();
      }
    });
    this.isSettingsOpen = false;
    this.getCurrentUser();
  }

  toggleSettings(): void {
    this.isSettingsOpen = !this.isSettingsOpen;
  }

  // Filter the notifications by category before grouping them
  groupByDay(): void {
    this.groupedNotifications = {};
    const notificationsToGroup = this.selectedCategoryId !== null
      ? this.notifications.filter(n => n.category_id === this.selectedCategoryId)
      : this.notifications;

    notificationsToGroup.forEach(notification => {
      const day = new Date(notification.set_at).toISOString().split('T')[0];
      if (!this.groupedNotifications[day]) {
        this.groupedNotifications[day] = [];
      }
      this.groupedNotifications[day].push(notification);
    });

    this.groupedDays = Object.keys(this.groupedNotifications).sort(
      (a, b) => new Date(b).getTime() - new Date(a).getTime()
    );

    this.groupedDays.forEach(day => {
      this.groupedNotifications[day].sort(
        (a, b) => new Date(b.set_at).getTime() - new Date(a.set_at).getTime()
      );
    });
  }

  // Called when the user changes the category filter
  applyCategoryFilter(): void {
    // Re-group the notifications using the selected category filter
    this.groupByDay();
  }

  loadNotifications(openLastGroup: boolean = false): void {
    if (this.loading || !this.hasMore) return;

    this.loading = true;
    const params = {
      unread_only: false,
      page: this.page,
      paginate: 1,
      limit: this.limit,
      channel: 1
    };

    this.notificationService.getNotifications(params).subscribe(
      (res) => {
        this.notifications = [...this.notifications, ...res];
        this.groupByDay();
        if (res.length < this.limit) {
          this.hasMore = false;
        }
        if (openLastGroup && this.groupedDays.length > 0) {
          const lastGroup = this.groupedDays[this.groupedDays.length - 1];
          this.openGroups[lastGroup] = true;
        }
        this.loading = false;
      },
      (error) => {
        console.error(error);
        this.loading = false;
      }
    );
  }

  toggleGroup(day: string): void {
    this.openGroups[day] = !this.openGroups[day];
  }

  isGroupOpen(day: string): boolean {
    if (this.openGroups[day] === undefined) {
      this.openGroups[day] = true;
    }
    return this.openGroups[day];
  }

  markUnreadNotificationsAsRead(): void {
    const unreadEntryIds = this.notifications
      .filter(n => !n.read)
      .map(n => n.entry_id);

    if (unreadEntryIds.length) {
      this.notificationService.markNotificationsAsRead({ entry_ids: unreadEntryIds }).subscribe(
        () => {
          this.notifications.forEach(n => {
            if (unreadEntryIds.includes(n.entry_id)) {
              n.read = new Date();
            }
          });
        },
        error => console.error('Error marking notifications as read', error)
      );
    }
  }

  markAllAsRead(): void {
    const unreadNotifications = this.notifications.filter(n => !n.read);
    if (unreadNotifications.length === 0) return;

    const payload: _CRM_NOT_1 = { mark_all_read: true };
    this.markAllAsReadLoading = true;
    this.notificationService.markNotificationsAsRead(payload).subscribe(() => {
        const now = new Date();
        this.notifications.forEach(notification => {
          if (!notification.read) {
            notification.read = now;
          }
        });

        this.notificationService.resetUnreadCount();
        this.groupByDay();
        this.markAllAsReadLoading = false;
      }, error => {
        this.markAllAsReadLoading = false;
        console.error('Error marking all notifications as read', error);
      }
    );
  }

  onShowMore(): void {
    if (!this.loading && this.hasMore) {
      this.page++;
      this.loadNotifications(true);
    }
  }

  onScroll(event: any): void {
    const target = event.target;
    const threshold = 100;
    if (
      target.scrollHeight - target.scrollTop - target.clientHeight < threshold &&
      this.hasMore &&
      !this.loading
    ) {
      this.page++;
      this.loadNotifications();
    }
  }

  getNotificationIcon(categoryId: number): string {
    switch (categoryId) {
      case 1: return 'fa-solid fa-shopping-cart';
      case 2: return 'fa-solid fa-credit-card';
      case 3: return 'fa-solid fa-briefcase';
      case 4: return 'fa-solid fa-note-sticky';
      case 5: return 'fa-solid fa-star';
      default: return 'fa-solid fa-bell';
    }
  }

  getNotificationIconClass(categoryId: number): string {
    switch (categoryId) {
      case 1: return 'order';
      case 2: return 'payment';
      case 3: return 'work-order';
      case 4: return 'note';
      case 5: return 'rating';
      default: return 'generic';
    }
  }

  getCategoryName(categoryId: number): string {
    switch (categoryId) {
      case 1:
        return this.translate.instant('notifications.categories.order');
      case 2:
        return this.translate.instant('notifications.categories.payment');
      case 3:
        return this.translate.instant('notifications.categories.workOrder');
      case 4:
        return this.translate.instant('notifications.categories.note');
      case 5:
        return this.translate.instant('notifications.categories.rating');
      default:
        return this.translate.instant('notifications.categories.generic');
    }
  }

  handleNotification(notification: NotificationsResponse): void {
    if (!notification.read) {
      this.notificationService.markNotificationsAsRead({
        entry_ids: [notification.entry_id]
      }).subscribe();
    }

    if (notification.company_id && notification.company_id !== this.currentCompanyId) {
      const company = this.companies.find(c => c.entity_id === notification.company_id);

      if (company) {
        this.storageService.saveSelectedCompany(company, true).subscribe(() => {
          this.toastService.infoToast(
            this.translate.instant('response_assets.companyChangedTo')
          );
          this.navigateToNotificationTarget(notification);
        });
        return;
      }
    }

    this.navigateToNotificationTarget(notification);
    this.closeMenu();
  }

  private navigateToNotificationTarget(notification: NotificationsResponse): void {
    this.currentCompanyId = this.storageService.getSelectedCompanyId();

    switch (notification.category_id) {
      case 1: // Order
      case 2: // Payment
      case 3: // Work order
      case 4: // Note
      case 5: // Rating
      default: // Generic
        if (notification.additional_data && notification.additional_data['custom_url']) {
          this.router.navigate([notification.additional_data['custom_url']]);
        }
        else if (notification.order_id) {
          this.router.navigate(['/orders/details', notification.order_id]);
        }
        break;
    }
    this.closeMenu();
  }

   closeMenu(): void {
    this.storageService.closeNotifications();
    this.isSettingsOpen = false;
  }

  getCurrentUser() {
    this.userService.getUser().subscribe((value) => {
      this.user_id = value.data.user_id;
      this.loadNotifications();
    });
  }

  hasUnreadNotifications(): boolean {
    return this.notifications.some(notification => !notification.read);
  }

  getUserCompanies() {
    this.userService.getUserEntityRelation().subscribe((companies) => {
      this.companies = companies;
    });
  }

  switchToCompany(companyId: string, companyName: string, event: MouseEvent): void {
    event.stopPropagation();

    const company = this.companies.find(c => c.entity_id === companyId);

    if (company) {
      this.storageService.saveSelectedCompany(company, true).subscribe(() => {
        this.closeMenu();
        this.router.navigate(['/dashboard']);
      });
    } else {
      console.log(`Company with ID ${companyId} not found in user's companies list`);
    }
  }
}
