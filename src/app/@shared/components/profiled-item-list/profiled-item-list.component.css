.assign-to-team-container {
  max-width: 600px; /* Adjust as necessary */
  background-color: #F2F3F4;
}


.search-results {
  /* Add styles for the search results list */
  box-sizing: border-box;
  width: 100%;
  position: absolute;
  overflow-y: auto;
  z-index: 1000 !important;
  background-color: #ffffff;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.3)
}

.avatar-circle {
  width: 30px;
  height: 30px;
  border-radius: 10%;
  background-color: #f0f0f0;
  /*display: flex;*/
  /*align-items: center;*/
  /*justify-content: center;*/
  color: #6c757d;
  font-weight: bold;
}

.selected-avatar {
  margin-left: 0;
  margin-right: 0;
}
.scrollable-list {
  overflow-y: auto; /* Enable vertical scrolling */
  max-height: calc(5 * 3.5rem); /* Adjust the multiplier based on the height of your list items */
}

.input-group {
  position: relative;
}


.list-group-item {
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 0 !important;
  border: none;
  border-bottom: 1px solid #e1e1e1;
}

.list-group-item:last-child {
  border-bottom: none;
}

.list-group-item:hover {
  background-color: #f8f9fa;
}

.list-group-item:focus {
  background-color: #f8f9fa;
}

.list-group-item.selected {
  background-color: #e2e5ff;
}
.avatar-circle.selected {
  width: 30px;
  height: 30px;
  border-radius: 10%;
  background-color: #448C74;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  font-weight: bold;
}

.view-schedule-btn {
  padding: 0.375rem 0.75rem;
}

.initials {
  font-size: 0.75rem; /* Adjust size as needed */
  cursor: default;
}

.absent {
  background-color: #f0f0f0;
  color: #6c757d;
}

.icon-border {
  border-left: 1px solid #e1e1e1; /* Adjust color as needed */
  padding-left: 5px; /* Adjust padding as needed */
  margin-left: 5px; /* Adjust margin as needed */
}

.text {
  display: inline-block; /* To ensure proper layout */
}

.click-container{
  border: 1px solid #dee2e6;
  border-radius: 3px 3px 0 0;
}

.form-control{

  border-radius: 5px !important;
}

.sub-display-text {
  font-size: smaller;
  color: #555;
}

.cross-button {
  position: absolute;
  right: 10px;
  z-index: 10;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.icon-selected {
  position: absolute;
  left: 10px;
  z-index: 15;
}

.icon-box {
  display: inline-block;
  width: 30px;
  height: 30px;
  background-color: #E6E6E6;
  color: #6C757D;
  text-align: center;
  line-height: 30px;
  margin-right: 10px;
  border-radius: 5px;
}

.end-display {
  position: absolute;
  right: 60px;
  z-index: 10;
}

.create-new {
  cursor: pointer;
  color: #007bff;
}

.item-avatar {
  position: relative;
  touch-action: pan-x pan-y;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  flex-shrink: 0;
  background-color: #f0f0f0;
  color: #565d64;
  font-weight: bold;
  margin-left: 0;
  margin-right: 0;
  transition: all 0.3s ease;
}

.item-avatar.absent {
  /*background-color: #ffdddd;   !* Light red background *!*/
  /*color: #a30000;              !* Dark red text *!*/
  /*border: 2px solid #ff5e5e;   !* Optional: red border to stand out *!*/
}

.item-avatar.absent::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: 4px solid #e57373;
  border-radius: 50%;
  box-sizing: border-box;
  z-index: 2;
}

.item-avatar.absent::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 4px;
  background-color: #e57373;
  transform: rotate(-45deg);
  top: 50%;
  left: 50%;
  transform-origin: center;
  translate: -50% -50%;
  z-index: 3;
}

.item-avatar.small {
  width: 30px;
  height: 30px;
}

.selectedItemsDiv.small {
  height: 38px;
  align-items: center;
}

.list-item-avatar {
  touch-action: pan-x pan-y;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  flex-shrink: 0;
  background-color: #f0f0f0;
  color: #6c757d;
  font-weight: bold;
  margin-left: 0;
  margin-right: 0;
}

.item-wrapper {
  white-space: nowrap;
  width: 30px; /* Match the initial width of the avatar */
  height: 30px; /* Match the initial height of the avatar */
  display: inline-block; /* Display items in a line */
  margin-right: 15px; /* Add margin between items */
  position: relative;
  transition: all 0.3s ease;
  }

.item-wrapper.small {
  width: 30px; /* Adjust as needed */
  height: 30px; /* Adjust as needed */
  margin-right: 7px; /* Adjust as needed */
}

.item-avatar-hover {
  touch-action: pan-x pan-y;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  flex-shrink: 0;
  background-color: #f0f0f0;
  color: #565d64;
  font-weight: bold;
  margin-left: 0;
  margin-right: 0;
  position: absolute;
  width: 45px; /* Adjust as needed */
  height: 45px; /* Adjust as needed */
  z-index: 8;
  top: 0;
  left: 0;
  transition: all 0.3s ease;
}

.item-avatar-hover.small {
  width: 40px;
  height: 40px;
}

.close-button {
  opacity: 1;
  visibility: visible;
  cursor: pointer;
  position: absolute;
  top: 2px;
  left: 29px;
  z-index: 30;
  transition: all 0.3s ease;
}

.close-button-hidden {
  opacity: 0;
  visibility: hidden;
  position: absolute;
  top: 7px;
  left: 18px;
  z-index: 30;
  transition: all 0.3s ease;
}

.item-label-tooltip {
  position: absolute;
  top: 100%; /* Adjust the distance from the item */
  left: 75%;
  transform: translateX(-50%);
  width: auto;
  background-color: #fff;
  border: 1px solid #ccc;
  padding: 5px;
  border-radius: 3px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  z-index: 100;
  display: none;
}

.item-wrapper:hover .item-label-tooltip {
  display: block;
}

.search-container .fa-magnifying-glass {
  position: absolute;
  right: 10px;
  z-index: 10;
}

.disabled-div {
    pointer-events: none;
    opacity: 0.6;
}

.no-right-border-radius {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
}

.no-left-border-radius {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
}

.no-top-border-radius {
  border-top-left-radius: 0 !important;
  border-top-right-radius: 0 !important;
}

.assignment-status-background {
  position: absolute;
  bottom: -12px;
  left: -2px;
  color: white;
  font-size: 20px;
}

.assignment-status-background.small {
  font-size: 17px;
  bottom: -2px;
  left: -2px;
}

.assignment-status {
  position: absolute;
  bottom: -10px;
  left: 0;
}

.assignment-status.small {
  bottom: 0;
  left: 0;
}
