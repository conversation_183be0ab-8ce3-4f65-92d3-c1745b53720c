<div id="clickGroupDiv" [style.z-index]="zIndex">
<h5 *ngIf="labelTranslationKey" class="mt-0 {{labelClass}}">{{labelTranslationKey | translate}}</h5>

  <div style="position: relative">
    <div id="selectedItemsDiv" class="d-flex align-items-center selectedItemsDiv" [ngClass]="{'small': small, 'flex-wrap': wrap}" style="position: relative;" *ngIf="selectedItems && selectedItems.length > 0">
      <div *ngFor="let item of selectedItems; let index = index" class="item-wrapper" style="" [ngClass]="{'small': small}" (mouseenter)="onMouseEnter(index)" (mouseleave)="onMouseLeave()" [ngbTooltip]="formatSelectedItem(item)" container="body">
        <div *ngIf="initialsKeys || itemImageKey" id="listItemImageInitialsDivNo_{{index}}" (mouseenter)="onMouseEnter(index)">
          <div class="item-avatar" [ngClass]="{'item-avatar-hover': hoveredSelectedItemIndex === index && !item['absence_description'] && !small, 'small': small, 'absent': item['absence_description']}">
            <div *ngIf="initialsKeys && (!item[itemImageKey] || showInitials)" class="item-avatar initials d-flex align-items-center justify-content-center" id="listItemInitialsSpanNo_{{index}}" [ngClass]="{'item-avatar-hover': hoveredSelectedItemIndex === index && !item['absence_description'] && !small, 'small': small}" [style.background-color]="!item['absence_description'] ? item['__backgroundColor__'] : null" (mouseenter)="onMouseEnter(index)">{{ getInitials(item) }}</div>
            <img *ngIf="item[itemImageKey] && !showInitials" draggable="false" class="item-avatar" loading="lazy" [ngClass]="{'item-avatar-hover': hoveredSelectedItemIndex === index && !item['absence_description'] && !small, 'small': small}" alt=""
              ngSrc="{{item[itemImageKey]}}"
              [width]="small ? 15 : 30"
              [height]="small ? 15 : 30"
              (mouseenter)="onMouseEnter(index)">
              <i *ngIf="hoveredSelectedItemIndex === index && showDeleteCross"
               class="fa-regular fa-circle-xmark fa-xl close-button text-muted"
               (click)="removeItem(item)"
               (mouseenter)="onMouseEnter(index)">
              </i>
          </div>
        </div>
        <i *ngIf="showAssignmentStatus && item['accepted_at']" class="fa-solid fa-circle-check text-success assignment-status" [ngClass]="{'small': small}" [style.z-index]="zIndex + 10"></i>
        <i *ngIf="showAssignmentStatus && item['declined_at']" class="fa-solid fa-circle-xmark text-danger assignment-status" [ngClass]="{'small': small}" [style.z-index]="zIndex + 10"></i>
        <i *ngIf="showAssignmentStatus && !item['declined_at'] && !item['accepted_at']" class="fa-solid fa-circle-question assignment-status" style="color: grey;" [ngClass]="{'small': small}" [style.z-index]="zIndex + 10"></i>
        <i *ngIf="showAssignmentStatus" class="fa-solid fa-circle assignment-status-background" [ngClass]="{'small': small}" style="" [style.z-index]="zIndex + 9"></i>
      </div>
    </div>
    <div id="noSelectedItemsDiv" class="d-flex align-items-center justify-content-center" [style.z-index]="zIndex" style="position: relative; height: 30px;" *ngIf="!selectedItems || selectedItems.length == 0 && showNoneSelected">
      <span  *ngIf="!employeeSelector && !resourceSelector" class="text-muted ">{{ noItemsSelectedTranslationKey | translate }}</span>
      <span *ngIf="employeeSelector && !resourceSelector" class="text-muted"> <i class="fa-regular fa-user-plus pe-1 "></i> {{ noEmployeeSelectedTranslationKey | translate }}</span>
      <span *ngIf="!employeeSelector && resourceSelector " class="text-muted"><i class="fa-regular fa-truck pe-1 "></i>{{ noResourceSelectedTranslationKey | translate }}</span>
    </div>
  </div>
</div>

