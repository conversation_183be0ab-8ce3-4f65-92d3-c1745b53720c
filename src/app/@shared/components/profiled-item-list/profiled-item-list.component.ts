import {
  Component,
  OnInit,
  Output,
  EventEmitter,
  Input,
  ElementRef, AfterViewInit, ViewChild, OnDestroy, ViewChildren
} from '@angular/core';
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {CommonModule, NgOptimizedImage} from "@angular/common";
import {FormsModule} from "@angular/forms";

import {StorageService} from "../../../@core/services/storage.service";
import {StandardImports} from "../../global_import";


@Component({
  selector: 'app-profiled-item-list',
  templateUrl: './profiled-item-list.component.html',
  styleUrls: ['./profiled-item-list.component.css'],
  standalone: true,
  imports: [StandardImports, NgOptimizedImage],
})
export class ProfiledItemListComponent implements OnInit {
  @ViewChild('focusThief') focusThief: ElementRef;
  @ViewChild('searchInput') searchInput: ElementRef;
  @ViewChildren('listItemDisplayNameSpan') listItemDisplayNameSpans: ElementRef[];

  @Input() initialsKeys: string[] = ['full_name'];
  @Input() itemImageKey: string;
  @Input() showAssignmentStatus: boolean = false;

  @Input() labelTranslationKey: string = '';
  @Input() labelClass: string = '';
  @Input() placeholderTranslationKey: string = '';
  @Input() noItemsSelectedTranslationKey: string = 'selectorini.noItemsSelected';
  @Input() employeeSelector: boolean = false
  @Input() resourceSelector: boolean = false
  @Input() noEmployeeSelectedTranslationKey: string = 'selectorini.noEmployeeSelected';
  @Input() noResourceSelectedTranslationKey: string = 'selectorini.noResourceSelected';
  @Input() small: boolean = false;
  @Input() showDeleteCross: boolean = true;
  @Input() wrap = true;

  @Input() zIndex: number = 0;

  @Input() selectedItems: any[] = [];
  @Input() itemIdKey: string;

  @Input() showNoneSelected: boolean = false;
  @Input() loading: boolean = false;

  initialsBackgroundColors = [
    '#7ec0ee', // Sky Blue
    '#f7a8a8', // Soft Pink
    '#98ff98', // Mint Green
    '#e6e6fa', // Lavender
    '#ffdab9', // Peach
    '#f08080', // Light Coral
    '#6495ed', // Cornflower Blue
    '#d8bfd8', // Thistle (Light Purple)
    '#7fffd4', // Aquamarine
    '#fffacd'  // Lemon Chiffon
  ];
  previousBackgroundColor: string = '';
  hoveredSelectedItemIndex: number | null = null;
  internalId: string;
  showInitials: boolean = false;

  @Output() itemSelectedEmitter: EventEmitter<{[key: string]: any }> = new EventEmitter<{ [key: string]: any }>();
  @Output() itemDeselectedEmitter: EventEmitter<any> = new EventEmitter<any>();
  @Output() createItemEmitter: EventEmitter<string> = new EventEmitter<string>();
  @Output() selectedItemsUpdatedEmitter: EventEmitter<any[]> = new EventEmitter<any[]>();

  constructor(private storageService: StorageService, private translate: TranslateService) {
    this.internalId = Math.random().toString(36).substring(2, 15)
  }

  ngOnInit(): void {
    // Set default settings if address search
    if (this.selectedItems && this.selectedItems.length > 0) {
      this.selectedItems.forEach(item => {
        item['__backgroundColor__'] = this.getInitialsBackgroundColor(item[this.itemIdKey]);
      });
    }

    this.storageService.showEmployeeInitials$.subscribe((showInitials) => {
      this.showInitials = showInitials;
    });

  }

  formatSelectedItem(item: any): string {
    let displayString: string = '';
    if (item) {
      this.initialsKeys.forEach((key, index) => {
        if (index > 0) {
          displayString += ' ';
        }
        displayString += item[key];
      });
    }
    if (item['absence_description']) {
      displayString += ' - ' + item['absence_description'];
    }
    if (this.showAssignmentStatus) {
      if (item['accepted_at']) {
        displayString += ' (' + this.translate.instant('selectorini.accepted') + ')';
      } else if (item['declined_at']) {
        displayString += ' (' + this.translate.instant('selectorini.declined') + ')';
      } else {
        displayString += ' (' + this.translate.instant('selectorini.pending') + ')';
      }
    }
    return displayString;
  }

  getInitialsBackgroundColor(itemId: string | number) {
    if (!itemId) {
      let backgroundColors = this.initialsBackgroundColors;
      if (this.previousBackgroundColor != '') {
        backgroundColors = backgroundColors.filter(color => color != this.previousBackgroundColor);
      }
      let randomIndex = Math.floor(Math.random() * backgroundColors.length);
      this.previousBackgroundColor = backgroundColors[randomIndex];
      return backgroundColors[randomIndex];
    }
    else {
      // Convert itemId to string in case it's a number

      const itemString = itemId.toString();

      // Simple hash function to convert the itemId string into a number
      let hash = 0;
      for (let i = 0; i < itemString.length; i++) {
        const char = itemString.charCodeAt(i);
        hash = (hash << 5) - hash + char;
        hash = hash & hash; // Convert to 32bit integer
      }

      // Ensure the hash code is positive
      hash = Math.abs(hash);

      const backgroundColors = this.initialsBackgroundColors;
      // Use the modulus operator to get an index within the bounds of backgroundColors array
      const index = hash % backgroundColors.length;
      this.previousBackgroundColor = backgroundColors[index];
      return backgroundColors[index];
    }
  }

  getInitials(item: any): string {
    let initials = ''
    if (item) {
      if (this.initialsKeys.length > 1) {
        let firstName = item[this.initialsKeys[0]];
        let lastName = item[this.initialsKeys[1]];
        firstName = firstName.trim();
        lastName = lastName.trim();
        initials = `${firstName[0]}${lastName[0]}`
        if (initials){
          initials = initials.toUpperCase()
        }
      } else if (this.initialsKeys.length == 1) {
        let displayName = item[this.initialsKeys[0]];
        displayName = displayName.trim();
        displayName = displayName.replace('  ', ' ')
        if (displayName.includes(' ')) {
          let [firstName, lastName] = displayName.split(' ');
          initials = `${firstName[0]}${lastName[0]}`;
          if (initials){
            initials = initials.toUpperCase()
          }
        } else {
          initials = displayName[0];
          if (initials){
            initials = initials.toUpperCase()
          }
          if (displayName.length > 1) {
            initials += displayName[1];
            if (initials){
              initials = initials.toUpperCase()
            }
          }
        }
      }
    }
    return initials
  }

  onMouseEnter(index: number): void {
    this.hoveredSelectedItemIndex = index;
  }

  onMouseLeave(): void {
    this.hoveredSelectedItemIndex = null;
  }

  removeItem(item: any): void {
    if (this.loading) {
      return;
    }
    this.selectedItems = this.selectedItems.filter(selectedItem => selectedItem !== item);
    this.itemDeselectedEmitter.emit(item);
    this.selectedItemsUpdatedEmitter.emit(this.selectedItems);

  }

}
