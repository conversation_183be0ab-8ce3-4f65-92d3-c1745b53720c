<div class="modal-header">
  <h5 class="modal-title">{{ editingViewId ? ('tablerino.updateView' | translate) : ('tablerino.saveView' | translate) }}</h5>
  <button type="button" class="btn-close" (click)="dismiss()"></button>
</div>
<div class="modal-body">
  <div class="form-group">
    <label for="viewName">{{ 'tablerino.viewName' | translate }}</label>
    <input type="text" class="form-control" id="viewName" [(ngModel)]="viewName" placeholder="{{ 'tablerino.enterViewName' | translate }}">
  </div>
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-secondary" (click)="dismiss()">{{ 'common.cancel' | translate }}</button>
  <button type="button" class="btn btn-primary" [disabled]="!viewName" (click)="save()">
    {{ editingViewId ? ('common.update' | translate) : ('common.save' | translate) }}
  </button>
</div>
