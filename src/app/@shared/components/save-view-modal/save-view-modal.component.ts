import { Component, Input, OnInit } from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import {TranslateModule} from "@ngx-translate/core";
import {CommonModule} from "@angular/common";
import {FormsModule} from "@angular/forms";

@Component({
  selector: 'app-save-view-modal',
  standalone: true,
  imports: [
    TranslateModule,
    CommonModule,
    FormsModule
  ],
  templateUrl: './save-view-modal.component.html'
})
export class SaveViewModalComponent implements OnInit {
  @Input() editingViewId: string | null = null;
  @Input() viewName: string = '';

  constructor(public activeModal: NgbActiveModal) {}

  ngOnInit(): void {}

  save() {
    this.activeModal.close({
      name: this.viewName,
      id: this.editingViewId
    });
  }

  dismiss() {
    this.activeModal.dismiss();
  }
}
