<div class="" *ngIf="selectedScheduleRepeatType">

  <!-- Start/end date -->
  <div class="row gx-2 mb-1" *ngIf="!viewSettings.consolidatedInvoiceView">
    <div class="col-12 col-lg-3 flex-grow-0 flex-shrink-0 mb-2" style="white-space: nowrap;">
      <label class="form-label mb-0 me-1">{{ "scheduleSetup.startStop.start" | translate }}</label>
      <div class="cursor-pointer col position-relative" (click)="!disabled && pickerinoStartDate.toggle($event)" (mouseenter)="startDateHovered = true;" (mouseleave)="startDateHovered = false;">
        <i class="fa-regular fa-calendar fa-lg me-1"></i>
        <span class="clickable-text">{{startDate ? utilsService.formatFullDayAndDate(this.startDate, false) : "scheduleSetup.startStop.immediately" | translate}}</span>
        <i *ngIf="this.startDate !== null && this.startDateHovered" class="fa-regular fa-xmark cursor-pointer px-1" (click)="clearStartDate($event)"></i>
        <app-datepickerino
          #pickerinoStartDate
          [popup]="true"
          [disableAfterDate]="this.endDate"
          [selectedDates]="[startDate!]"
          (datesSelectedEmitter)="startDateChanged($event[0])"
        ></app-datepickerino>
      </div>
    </div>
    <div class="col-12 col-lg-auto mb-2">
      <label class="form-label mb-0 me-1">{{ "scheduleSetup.startStop.stop" | translate }}</label>
      <div class="cursor-pointer col position-relative" (click)="!disabled && pickerinoStartEnd.toggle($event)" (mouseenter)="endDateHovered = true;" (mouseleave)="endDateHovered = false;">
        <i class="fa-regular fa-calendar fa-lg me-1"></i>
        <span class="clickable-text">{{endDate ? utilsService.formatFullDayAndDate(this.endDate, true) : "scheduleSetup.startStop.never" | translate}}</span>
        <i *ngIf="this.endDate !== null && this.endDateHovered" class="fa-regular fa-xmark cursor-pointer px-1" (click)="clearEndDate($event)"></i>
        <app-datepickerino
          #pickerinoStartEnd
          [popup]="true"
          [disableBeforeDate]="this.startDate"
          [selectedDates]="[endDate!]"
          (datesSelectedEmitter)="endDateChanged($event[0])"
        ></app-datepickerino>
      </div>
    </div>
  </div>

  <div id="scheduleRow1" class="row gx-2">
    <div id="scheduleRepeatTypeDiv" class="col-12 col-lg-3 mb-1">
      <label for="scheduleType" class="">{{ "orderSchedules.repeat" | translate }}</label>
      <app-selectorini
        [disabled]="disabled"
        *ngIf="scheduleRepeatTypes"
        id="scheduleType"
        [predefinedSearchResults]="scheduleRepeatTypes"
        [searchMainDisplayKeys]="['schedule_repeat_type_name']"
        [selectedItem]="selectedScheduleRepeatType"
        (itemSelectedEmitter)="onScheduleRepeatTypeChange($event)"
        [searchable]="false"
        [showCrossButton]="false"
      ></app-selectorini>
    </div>

    <div id="scheduleEveryDiv" class="col-12 col-lg-auto mb-1">
      <label for="scheduleEvery" class="">{{ "orderSchedules.every" | translate }}</label>
      <app-input
        id="scheduleEvery"
        [type]="'number'"
        [control]="scheduleEveryControl"
        [inputSuffix]="repeatSuffixes[selectedScheduleRepeatType.schedule_repeat_type_id] | translate"
        [editMode]="true"
        [centerWithNoPadding]="true"
        [showErrorIcon]="false"
        [inputFieldMinWidthPx]="60"
        [inputFieldMaxWidthPx]="60"
        (valueChange)="onScheduleEveryChange($event)"
      ></app-input>
    </div>

    <!-- Weekdays -->
    <div id="weekdaysDiv" *ngIf="selectedScheduleRepeatType.schedule_repeat_type_id == 1" class="form-group col-12 col-lg-2 mb-1" style="min-width: 230px;">
      <label class="mb-0 form-label">{{ "orderSchedules.weekdays" | translate }}</label>
      <div class="row mt-1" id="weekday-row">
        <div *ngFor="let day of days" class="col-1" style="margin-right: 7px">
          <div class="checkbox-wrapper-8">
            <input [disabled]="disabled" class="tgl tgl-skewed" id="{{uniqueIdPrefix}}-day-{{day.value}}" type="checkbox" [ngModel]="day.active" (ngModelChange)="day.active = $event; updateWeekdays(day)"/>
            <label class="tgl-btn" title="{{day.name}}" [attr.data-tg-off]="day.abbreviation" [attr.data-tg-on]="day.abbreviation" for="{{uniqueIdPrefix}}-day-{{day.value}}"></label>
          </div>
        </div>
      </div>
    </div>

    <!-- Monthly setup -->
    <div id="monthlyDiv"
         *ngIf="selectedScheduleRepeatType.schedule_repeat_type_id == 2"
         [class.col-12]="true"
         [class.col-lg-2]="scheduleUseDate"
         [class.col-lg-4]="!scheduleUseDate"
         class="d-flex flex-wrap mb-1">

      <div id="schedulerMonthlyNthWeekDiv" class="d-flex" *ngIf="!scheduleUseDate">
        <div id="schedulerMonthlyNthWeekInnerDiv" class="col-12 col-sm-6 col-lg-4 mb-2">
          <label class="mb-0 form-label">{{ "orderSchedules.nthLabel" | translate }}</label>
          <app-selectorini
            id="nthWeek"
            [disabled]="disabled"
            [predefinedSearchResults]="nthWeeks"
            [searchMainDisplayKeys]="['display']"
            [selectedItem]="nthWeeks[selectedNthWeekdayIndex]"
            (itemSelectedEmitter)="onNthWeekChange($event)"
            [searchable]="false"
            [showCrossButton]="false"
            [enableScrolling]="false"
          ></app-selectorini>
        </div>

        <div id="schedulerMonthlyNthWeekdayDiv" class="col-12 col-sm-6 col-lg-auto mb-2 ms-lg-1">
          <label class="mb-0 form-label" style="visibility: hidden;">{{ "orderSchedules.nthWeekdayLabel" | translate }}</label>
          <div class="d-flex">
            <app-selectorini
              id="nthWeekDay"
              [disabled]="disabled"
              [predefinedSearchResults]="stockWeekdays"
              [searchMainDisplayKeys]="['display']"
              [selectedItem]="stockWeekdays[selectedNthWeekdayIndex]"
              (itemSelectedEmitter)="onNthWeekdayChange($event)"
              [searchable]="false"
              [showCrossButton]="false"
              [enableScrolling]="false"
            ></app-selectorini>
            <div class="d-flex align-items-center ms-1" [ngbTooltip]="'orderSchedules.monthDateViewTooltip' | translate" (click)="onUseDateSwitchChange()">
              <i class="fa-regular fa-calendar-day cursor-pointer fa-xl"></i>
            </div>
          </div>
        </div>
      </div>

      <div id="schedulerMonthlyDateDiv" class="col-12 col-sm-6 col-lg-auto mb-2" *ngIf="scheduleUseDate">
        <label class="mb-0 form-label">{{ "orderSchedules.dateLabel" | translate }}</label>
        <div class="d-flex">
          <app-selectorini
            [disabled]="disabled"
            [directSelection]="true"
            [showChevron]="false"
            [selectedMainDisplayCentered]="true"
            [predefinedSearchResults]="scheduleMonthDates"
            [selectedItem]="selectedScheduleMonthDate"
            [searchMainDisplayKeys]="['display']"
            (itemSelectedEmitter)="onScheduleDateChange($event)"
          ></app-selectorini>
          <div class="d-flex align-items-center ms-1" [ngbTooltip]="'orderSchedules.monthWeekViewTooltip' | translate" (click)="onUseDateSwitchChange()">
            <i class="fa-regular fa-calendar-week cursor-pointer fa-xl"></i>
          </div>
        </div>
      </div>

    </div>
  </div>

  <div class="row mb-1" id="scheduleRow2">
    <div class="col-12">
      <span class="text-muted font-12">{{noWeekdaysSelected ? '' : ("orderSchedules.descriptionLabel." + (paymentView ? (viewSettings.consolidatedInvoiceView ? 'consolidatedInvoice' : 'payment') : 'workOrder')) | translate }} {{scheduleDescription}}</span>
    </div>
  </div>

  <!--  Instances in advance  -->
  <div *ngIf="!paymentView" class="mb-2">
    <label class="mb-0 form-label">{{ "orderSchedules.inAdvance" | translate }}</label>
    <div class="col-5">
      <app-input
        [control]="scheduleInstancesInAdvanceControl"
        [editMode]="true"
        [inputSuffix]="'orderSchedules.inAdvance.suffix' | translate"
        [centerWithNoPadding]="true"
        [inputFieldMinWidthPx]="60"
        [inputFieldMaxWidthPx]="60"
        [emitChangeOnBlurOnly]="true"
        (valueChange)="updateInstancesInAdvance($event)"
      ></app-input>
    </div>
    <div class="text-muted font-12 mt-1">{{"orderSchedules.inAdvance.tooltip1" | translate}} {{scheduleInstancesInAdvanceControl.value}} {{"orderSchedules.inAdvance.tooltip2" | translate}}</div>
  </div>

  <div class="row">
    <!--  Work orders quickview  -->
    <div class="d-flex flex-wrap justify-content-start col-12 col-lg-6" [ngClass]="{'mt-2': !paymentView}">
      <!--   First work order   -->
      <div class="ps-1 mt-2" style="border-left: 2px solid #cbcfd1;">
        <div class="fw-bold font-12">{{("orderSchedules.first." + (paymentView ? (viewSettings.consolidatedInvoiceView ? 'consolidatedInvoice' : 'payment') : 'workOrder')) | translate}}</div>
        <div *ngIf="scheduleExecutionDates.length > 0">{{utilsService.formatDateWdDYM(scheduleExecutionDates[0], false, false)}}</div>
      </div>

      <!--   Last work order   -->
      <div *ngIf="endDate" class="ps-2 mt-2" style="border-left: 2px solid #cbcfd1;">
        <div class="fw-bold font-12">{{("orderSchedules.last." + (paymentView ? 'payment' : 'workOrder')) | translate}}</div>
        <div *ngIf="scheduleExecutionDates.length > 0">{{utilsService.formatDateWdDYM(scheduleExecutionDates[scheduleExecutionDates.length - 1], false, false)}}</div>
      </div>

      <!--   Total work orders   -->
      <div *ngIf="endDate" class="ps-2 mt-2" style="border-left: 2px solid #cbcfd1;">
        <div class="fw-bold font-12">{{"orderSchedules.total" | translate}}</div>
        <div>{{scheduleExecutionDates.length}}</div>
      </div>
    </div>
  </div>

</div>
