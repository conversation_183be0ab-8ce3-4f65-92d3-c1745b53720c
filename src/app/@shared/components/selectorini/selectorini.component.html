<div *ngIf="disableFocusOnLoad" id="focusThief" tabIndex="-1" #focusThief></div>
<div id="clickGroupDiv" class="clickGroup" [style.z-index]="zIndex">
  <h5 *ngIf="labelTranslationKey" class="mt-0 {{labelClass}}">{{labelTranslationKey | translate}}</h5>

  <div style="position: relative">
    <div class="input-group search-container" [style.z-index]="zIndex">
      <i *ngIf="!selectedItem && showMagnifyingGlass && !loading && searchable" class="fa-regular fa-magnifying-glass"></i>
      <app-spinner *ngIf="loading" style="position: absolute; right: 10px;" [style.z-index]="zIndex + 10"></app-spinner>
      <i *ngIf="selectedItem && showIcons && selectedIconClass" [ngClass]="selectedIconClass ? ['icon-selected', selectedIconClass] : ''"></i>
      <button *ngIf="!loading && ((selectedItem && showCrossButton && !disabled) || showCrossButtonOnInit)" type="button" class="btn-close text-muted me-0 cross-button" aria-label="Close" (click)="deselectItem()"></button>
      <i *ngIf="directSelection && showChevron" class="fa-regular fa-chevron-down cross-button cursor-pointer" (click)="activateDropDown()"></i>
      <span *ngIf="selectedItem && selectedEndDisplayKey" class="end-display">{{formatSelectedEndDisplay(selectedItem)}}</span>
      <div #searchInput *ngIf="customNgTemplate && directSelection && selectedItem" class="form-control d-flex align-items-center cursor-pointer px-0" (click)="activateDropDown()">
        <ng-container *ngTemplateOutlet="selectedItem['ngTemplate']; context: { item: selectedItem }"></ng-container>
      </div>
      <input
        *ngIf="!customNgTemplate || !directSelection || !selectedItem"
        #searchInput
        type="text"
        class="form-control"
        placeholder="{{placeholderTranslationKey | translate}}"
        [(ngModel)]="searchTerm"
        [formControl]="control"
        (input)="searchable ? search() : $event.preventDefault()"
        (click)="activateDropDown()"
        (keydown)="onSearchInputKeydown($event)"
        [ngClass]="inputFieldClasses.concat(customInputFieldClasses)"
        [ngStyle]="{'caret-color': !searchable ? 'transparent' : ''}"
      >
      <span *ngIf="showCustomAddressTag" class="text-muted ms-2 end-display">{{selectedItem['custom_tag']}}</span>
    </div>
    <div id="dropDownListDiv" [style.z-index]="zIndex + 5">
      <ul id="dropDownList" class="list-group p-0 search-results" [style.width.px]="dropDownWidthPx" [ngClass]="{'scrollable-list': enableScrolling}" [style.z-index]="zIndex + 6" *ngIf="showItemsOnFocus && showDropDown && (searchResults.length >= 1 || showCreateItem)">

        <!--   For each item of search results   -->
        <li id="{{internalId}}_listItemNo_{{index}}" *ngFor="let item of searchResults; let index = index"
            tabindex="0"
            [style.z-index]="zIndex + 10"
            [ngStyle]="{'background-color': item['__selected__'] ? 'rgba(68, 140, 116, 0.25)' : null}"
            class="list-group-item d-flex justify-content-between align-items-center mb-0 p-0"
            [ngClass]="item[searchMainDisplayKeys[0]] == '__createNewItem__' ? 'create-new' : ''"
            (click)="selectItem($event, item)"
            (keydown)="onItemKeydown($event, item, index)"
            [style.min-height.px]="listItemMinHeightPx"
        >

          <!--   Item for create-new function     -->
          <div *ngIf="item[searchMainDisplayKeys[0]] == '__createNewItem__'" class="ms-2 d-flex align-items-center">
            <i *ngIf="showPlusOnCreateItem" class="mdi mdi-plus mdi-18px"></i>
            <span>{{ createItemTranslationKey | translate }}</span>
          </div>

          <!--   Regular item     -->
          <div *ngIf="!customNgTemplate && item[searchMainDisplayKeys[0]] != '__createNewItem__'" [ngClass]="itemDisabled(item) ? 'disabled-div' : ''" id="listItemInnerDivNo_{{index}}" class="d-flex align-items-center ms-1" [ngbTooltip]="item['absence_description']">
            <div  *ngIf="initialsKeys || itemImageKey" id="listItemImageInitialsDivNo_{{index}}" class="avatar-circle me-2" [ngClass]="{'avatar-circle-image': item[itemImageKey], 'selected-avatar': item['__selected__'] && !item[itemImageKey], 'absent': item['absence_description']}" style="min-width: 30px;">
              <span *ngIf="initialsKeys && !item[itemImageKey]" id="listItemInitialsSpanNo_{{index}}" class="initials" [ngClass]="{}">{{ getInitials(item) }}</span>
              <img *ngIf="item[itemImageKey]" draggable="false" loading="lazy" class="list-item-avatar" [ngClass]="{'selected-image': item['__selected__'] && item[itemImageKey]}" alt="no-image"
                   ngSrc="{{item[itemImageKey]}}"
                   width="30"
                   height="30">
            </div>
            <div *ngIf="showIcons" class="icon-box">
<!--              <fa-icon [icon]="icons[item['icon_id']]" size="1x"></fa-icon>-->
            </div>
            <div id="listItemDisplayNameDivNo_{{index}}">
              <div id="listItemDisplayNameInnerDivNo_{{index}}" class="row w-100">
                <span #listItemDisplayNameSpan [ngClass]="getSearchItemClasses(item)" style="white-space: nowrap;">{{formatSearchItem(item)}}</span>
                <span class="sub-display-text" style="white-space: nowrap;" *ngIf="searchSubDisplayKeys || itemDisabled(item)"> {{ getSubDisplayString(item) }}</span>
              </div>
            </div>
          </div>

          <!--  Regular item | NgTemplate  -->
          <ng-container *ngTemplateOutlet="item['ngTemplate']; context: { item: item }"></ng-container>

          <div *ngIf="searchEndDisplayKey && item[searchMainDisplayKeys[0]] != '__createNewItem__'" class="d-flex justify-content-end">
            <span class="me-2">{{formatSearchEndDisplay(item)}}</span>
          </div>
        </li>
      </ul>
    </div>
  </div>
</div>

