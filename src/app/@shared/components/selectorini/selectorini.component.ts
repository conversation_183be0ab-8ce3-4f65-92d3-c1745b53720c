import {
  Component,
  OnInit,
  Output,
  EventEmitter,
  Input,
  OnChanges,
  SimpleChanges,
  HostListener,
  ElementRef, AfterViewInit, ViewChild, OnDestroy, ViewChildren, TemplateRef
} from '@angular/core';
import {TranslateModule, TranslateService} from "@ngx-translate/core";
import {currencyFormat} from "../../../@core/utils/utils.service";
import {CommonModule, NgOptimizedImage} from "@angular/common";
import {FormControl, FormsModule, ReactiveFormsModule} from "@angular/forms";
import {SpinnerComponent} from "../spinner/spinner.component";
import {EMPTY, Observable, Subject} from "rxjs";
import {switchMap, takeUntil} from "rxjs/operators";
import {StandardImports} from "../../global_import";


@Component({
  selector: 'app-selectorini',
  templateUrl: './selectorini.component.html',
  styleUrls: ['./selectorini.component.css'],
  standalone: true,
  imports: [StandardImports, SpinnerComponent, NgOptimizedImage],
})
export class SelectoriniComponent implements OnInit, OnChanges, AfterViewInit, OnDestroy {
  @ViewChild('focusThief') focusThief: ElementRef;
  @ViewChild('searchInput') searchInput: ElementRef;
  @ViewChildren('listItemDisplayNameSpan') listItemDisplayNameSpans: ElementRef[];

  @Input() multiSelect: boolean = false;
  @Input() directSelection: boolean = false;
  @Input() searchable: boolean = true;
  @Input() searchMainDisplayKeys: string[] = ['dummyKey'];
  @Input() searchSubDisplayKeys: string[];
  @Input() searchDisplaySeparator: string = ' ';
  @Input() mainDisplayCommaSeparatorAtIndices: number[] = [];
  @Input() searchDisplayCommaSeparatorAtIndices: number[] = [];
  @Input() searchMainParenthesisedDisplayKeys: string[]
  @Input() searchEndDisplayKey: string;
  @Input() searchEndDisplayIsAmount: boolean = false;
  @Input() searchMainDisplayBolded: boolean = true;
  @Input() fallBackDisplayKeys: string[] = [];
  @Input() displayClasses: string[] = [];
  @Input() itemClassKey: string;

  @Input() customNgTemplate: TemplateRef<any> | null = null;

  @Input() addressSearch: boolean = false;
  @Input() showPropertyType: boolean = false;

  @Input() initialsKeys: string[];
  @Input() itemImageKey: string;

  @Input() control: FormControl = new FormControl();

  @Input() selectedMainDisplayKeys: string[];
  @Input() selectedDisplaySeparator: string = ' ';
  @Input() selectedMainParenthesisedDisplayKeys: string[] = [];
  @Input() selectedEndDisplayKey: string;
  @Input() selectedEndDisplayIsAmount: boolean = false;
  @Input() selectedEndDisplayInParentheses: boolean = false;
  @Input() selectedMainDisplayCentered: boolean = false;

  @Input() labelTranslationKey: string = '';
  @Input() labelClass: string = '';
  @Input() placeholderTranslationKey: string = '';

  @Input() showIcons: boolean = false;
  @Input() useMissingIconOffset = true;
  @Input() showItemsOnFocus: boolean = true;
  @Input() showMagnifyingGlass: boolean = true;
  @Input() showCrossButton: boolean = true;
  @Input() showCrossButtonOnInit: boolean = false;
  @Input() enableScrolling: boolean = true;
  @Input() dropDownWidthPx: number | null = null;
  @Input() showChevron: boolean = true;

  @Input() zIndex: number = 0;

  @Input() showCreateItem: boolean = false;
  @Input() showPlusOnCreateItem: boolean = true;
  @Input() createItemTranslationKey: string = '';

  @Input() searchFunction: Function;
  @Input() searchOnFocus: boolean = false;
  @Input() predefinedSearchResults: { [key: string]: any }[] | null = null;
  @Input() predefinedSearchKeys: string[];
  @Input() icons: { [key: number]: string };
  @Input() minWidthPercentage: number = 100;
  @Input() maxWidthPercentage: number = 100;
  @Input() disableOnSelect: boolean = false;
  @Input() hideDropDownOnSelect: boolean = true;
  @Input() disabledSearchResults: { [key: string]: any }[] = [];
  @Input() disabledTranslationKey: string = 'selectorini.disabled';
  @Input() disabledItemSubStringKey: string = '';

  @Input() selectedItem: any;
  @Input() selectedItems: any[] = [];
  @Input() itemIdKey: string;
  @Input() clearComponent: Observable<void>

  @Input() disableFocusOnLoad: boolean = false;
  @Input() loading: boolean = false;
  @Input() disabled: boolean = false;
  @Input() searchInputRegex: string;
  @Input() customInputFieldClasses: string[] = [];
  @Input() listItemMinHeightPx: number = 45;

  searchTerm: string;
  searchResults: { [key: string]: any }[] = [];
  showDropDown: boolean = false;
  selectedIconClass: string | null;
  showCustomAddressTag: boolean = false;

  internalId: string;

  inputFieldClasses: string[] = [];

  private destroy$: Subject<void> = new Subject<void>();
  private cancelSearch$: Subject<void> = new Subject<void>();

  @Output() itemSelectedEmitter: EventEmitter<{[key: string]: any }> = new EventEmitter<{ [key: string]: any }>();
  @Output() itemDeselectedEmitter: EventEmitter<any> = new EventEmitter<any>();
  @Output() createItemEmitter: EventEmitter<string> = new EventEmitter<string>();
  @Output() selectedItemsUpdatedEmitter: EventEmitter<any[]> = new EventEmitter<any[]>();

  constructor(private translate: TranslateService, private el: ElementRef) {
    this.internalId = Math.random().toString(36).substring(2, 15)
  }

  ngOnInit(): void {
    // Set default settings if address search
    if (this.addressSearch) {
      this.showCreateItem = true;
      this.createItemTranslationKey = 'selectorini.createNewAddress';
      this.showPlusOnCreateItem = false;
      this.showIcons = false;
      this.searchMainDisplayKeys = ['dummyDisplayKey'];
      this.searchSubDisplayKeys = [];
      this.placeholderTranslationKey = this.placeholderTranslationKey == '' ? 'selectorini.searchAddressPlaceholder' : this.placeholderTranslationKey;
    }

    if (this.directSelection) {
      this.searchable = false;
      this.showCrossButton = false;
      this.disableOnSelect = false;
      this.searchMainDisplayBolded = false;
    }

    if (this.control.value) {
      this.searchTerm = this.control.value;
    }

    if (this.maxWidthPercentage < this.minWidthPercentage) {
      this.minWidthPercentage = this.maxWidthPercentage;
    }

    if (this.selectedItem) {
      this.selectItem(null, this.selectedItem, false);
    }

    this.setSearchResults(this.predefinedSearchResults);

    this.inputFieldClasses = this.computeInputFieldClasses();

    if (this.clearComponent) {
      this.clearComponent.subscribe(() => {
        this.selectedItem = null;
        this.selectedItems = [];
        this.searchTerm = '';
      });
    }

  }

  computeInputFieldClasses() {
    let classes: string[] = []
    if (this.showIcons && this.selectedItem && (this.selectedIconClass || this.useMissingIconOffset)) {
      classes.push('ps-4');
    }

    if (!this.searchable) {
      classes.push('cursor-pointer');
    }

    if (this.selectedMainDisplayCentered) {
      classes.push('text-center');
    }

    classes = classes.concat(this.displayClasses);

    if (this.selectedItem && this.itemClassKey && this.selectedItem[this.itemClassKey]) {
      classes = classes.concat(this.selectedItem[this.itemClassKey])
    }

    return classes;
  }

  getSearchItemClasses(item: any) {
    let classes: string[] = [];

    if (this.searchMainDisplayBolded) {
      classes.push('fw-bold');
    }

    if (item && this.itemClassKey && item[this.itemClassKey]) {
      classes = classes.concat(item[this.itemClassKey])
    }

    return classes;

  }

  ngOnChanges(changes: SimpleChanges) {
    if (changes['predefinedSearchResults']) {
      this.setSearchResults(this.predefinedSearchResults);
    }

    if (changes['selectedItems']) {
      this.setSearchResults(this.predefinedSearchResults)
    }

    if (changes['selectedItem']) {
      this.inputFieldClasses = this.computeInputFieldClasses();
      this.selectItem(null, this.selectedItem, false);
    }

    if (changes['disabled']) {
      if (this.disabled) {
        this.control.disable({emitEvent: false});
      } else {
        this.control.enable({emitEvent: false});
      }
    }
  }

  ngAfterViewInit() {
    if (this.disableFocusOnLoad) {
      this.focusThief.nativeElement.focus();
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.cancelSearch$.next();
    this.cancelSearch$.complete();
  }

  @HostListener('document:click', ['$event'])
  handleClick(event: Event): void {
    const clickGroupElement = this.el.nativeElement.querySelector('.clickGroup');

    if (clickGroupElement && !clickGroupElement.contains(event.target)) {
      this.showDropDown = false;
    }
  }

  // onSearchInputFocus(event: any): void {
  //   if (this.showItemsOnFocus) {
  //     event.stopPropagation();
  //     this.showDropDown = true;
  //   }
  // }

  search(): void {
    if (!this.searchable) {
      return;
    }

    this.showCustomAddressTag = false;

    // Emit a cancellation signal to cancel any ongoing requests
    this.cancelSearch$.next();

    if (this.predefinedSearchResults != null && !this.searchFunction) {
      if (this.searchTerm.length > 0) {
        this.setSearchResults(
          this.predefinedSearchResults.filter(item => {
            const itemValues = this.predefinedSearchKeys.map(key => item[key] ? item[key].toLowerCase() : '');
            return itemValues.some(value => value.includes(this.searchTerm.toLowerCase()));
          })
        );
      } else {
        this.setSearchResults(this.predefinedSearchResults)
      }
    }
    else {
      if ((this.searchTerm && this.searchTerm.length >= 1) || this.searchOnFocus) {
        if (this.addressSearch && this.searchTerm) {
          this.searchTerm = this.searchTerm.replace(/[^0-9a-zA-ZæøåäöÆØÅÄÖ ,.#\/-]+/g, '');
        }


        this.loading = true;
        this.searchFunction(this.searchTerm)
          .pipe(
            switchMap((result: { [key: string]: any }[]) => {
              return this.handleSearchResult(result);
            }),
            takeUntil(this.cancelSearch$) // Cancel ongoing requests
          )
          .subscribe(() => {
            // Hide loading spinner when the search is completed
            setTimeout(() => {
              this.loading = false;
            }, 400);
          });
      } else {
        // If the search term is less than 3 characters, reset search results
        this.setSearchResults([]);
      }
    }
  }

  handleSearchResult(result: { [key: string]: any }[]): Observable<void> {
    this.setSearchResults(result);
    this.showDropDown = true;
    setTimeout(() => {
      this.loading = false;
    }, 350);
    return EMPTY; // Return an empty observable to complete the stream
  }

  selectItem(event: Event | null, item: { [key: string]: any }, emitSelected: boolean = true): void {
    if (!item || this.loading || this.itemDisabled(item)) {
      return;
    }

    if (item['__selected__']) {
      this.removeItem(item);
      return;
    }

    if (this.customNgTemplate) {
      item['ngTemplate'] = this.customNgTemplate
    }

    this.searchTerm = '';
    this.setSearchResults(this.predefinedSearchResults, true)

    // Logic for handling disabled items
    if (this.itemDisabled(item)) {
      return;
    }

    // Single select logic for handling selection of item
    if (!this.multiSelect) {
      if (item[this.searchMainDisplayKeys[0]] === '__createNewItem__') {
        this.createItemEmitter.emit(this.searchTerm);
        return;
      }
      this.selectedItem = item;
      this.searchTerm = this.formatSelectedItem(item);

      if (emitSelected) {
        this.itemSelectedEmitter.emit(item);
      }

      this.showDropDown = false;
      if (this.showIcons && this.icons) {
        this.selectedIconClass = this.getIconClass(item['icon_id'])
      }

      if (this.addressSearch && this.selectedItem['custom_tag']) {
        this.showCustomAddressTag = true;
      }
    }

    // Multi select logic for handling selection of item
    else {
      this.selectedItems.push(item);
      // filter out all selected items from search results
      // if (this.itemIdKey) {
      //   this.searchResults = this.searchResults.filter(searchResult => !this.selectedItems.map(item => item[this.itemIdKey]).includes(searchResult[this.itemIdKey]));
      // }
      // else {
      //   this.searchResults = this.searchResults.filter(item => !this.selectedItems.includes(item));
      // }
      item['__selected__'] = true;

      if (emitSelected) {
        this.itemSelectedEmitter.emit(item);
      }

      if (this.hideDropDownOnSelect) {
        this.showDropDown = false;
      }

      this.selectedItemsUpdatedEmitter.emit(this.selectedItems);

      // Call stopPropagation to hinder the menu from closing
      if (event) {
        event.stopPropagation();
      }
    }

    if (this.disableOnSelect) {
      this.disabled = true;
      this.control.disable({emitEvent: false});
    }

  }

  deselectItem(emitDeselected: boolean = true): void {
    this.disabled = false;
    this.control.enable({emitEvent: false});
    this.selectedItem = undefined;
    this.searchTerm = '';
    this.showCustomAddressTag = false;
    if (emitDeselected) {
      this.itemDeselectedEmitter.emit();
      this.selectedItemsUpdatedEmitter.emit(this.selectedItems);
    }
  }

  removeItem(item: any): void {
    if (this.loading) {
      return;
    }
    this.selectedItems = this.selectedItems.filter(selectedItem => selectedItem !== item);
    this.itemDeselectedEmitter.emit(item);
    this.selectedItemsUpdatedEmitter.emit(this.selectedItems);
    this.showDropDown = false;
  }

  onSearchInputKeydown(event: KeyboardEvent): void {
    const regex = new RegExp(this.searchInputRegex);

    // Check the key press as before
    if (!regex.test(event.key)) {
      event.preventDefault();
      return;
    }

    if (this.searchInputRegex && !new RegExp(this.searchInputRegex).test(event.key)) {
      event.preventDefault();
      return;
    }

    if (this.searchResults.length > 0) {
      if (event.key === 'ArrowDown') {
        event.preventDefault(); // Prevent the default scroll behavior
        const nextItem = document.getElementById(`${this.internalId}_listItemNo_${0}`);
        if (nextItem) {
          nextItem.focus();
        }
      }
      if (event.key === 'ArrowUp') {
        event.preventDefault(); // Prevent the default scroll behavior
        const nextItem = document.getElementById(`${this.internalId}_listItemNo_${this.searchResults.length - 1}`);
        if (nextItem) {
          nextItem.focus();
        }
      }
      if (event.key === 'Escape' || event.key === 'Esc') {
        event.preventDefault()
        this.searchInput.nativeElement.blur()
        this.showDropDown = false;
      }
      else {
        if (!this.searchable) {
          event.preventDefault();
        }
      }
    }
    // Update the search term after key press
    setTimeout(() => {
      const inputValue = this.searchInput.nativeElement.value;
      // Remove any disallowed characters
      const sanitizedValue = inputValue.replace(/[^0-9a-zA-ZæøåäöÆØÅÄÖ ,.#\/-]+/g, '');

      if (inputValue !== sanitizedValue) {
        this.searchInput.nativeElement.value = sanitizedValue;
      }

      // Update the searchTerm variable to match the sanitized value
      this.searchTerm = sanitizedValue;
      this.search();
    }, 0);
  }

  onItemKeydown(event: KeyboardEvent, item: any, currentIndex: number): void {
    if (event.key === 'Escape' || event.key === 'Esc') {
      event.preventDefault()
        this.searchInput.nativeElement.blur()
      this.showDropDown = false;
      return;
    }

    if (event.key === 'Enter') {
      this.selectItem(null, item);
    }

  // Handle only arrow up and arrow down keys
  if (event.key === 'ArrowUp' || event.key === 'ArrowDown') {
    event.preventDefault(); // Prevent the default scroll behavior

    let nextIndex = currentIndex + (event.key === 'ArrowDown' ? 1 : -1);
    // Ensure the next index remains within the bounds of the list
    if (nextIndex >= this.searchResults.length) {
      nextIndex = 0; // Optionally loop to the start
    } else if (nextIndex < 0) {
      nextIndex = this.searchResults.length - 1; // Optionally loop to the end
    }

    // Find the next list item and focus it
    const nextItem = document.getElementById(`${this.internalId}_listItemNo_${nextIndex}`);
    if (nextItem) {
      nextItem.focus();
    }
  }
}

  getSubDisplayString(item: any): string {
    if (this.addressSearch) {
      let substring = '';
      // if (item['property_type_id'] && ![null, 6].includes(item['property_type_id'])) {
      //   substring += item['property_type_name']
      // }
      return substring
    }
    let subDisplayString = '';
    if (this.searchSubDisplayKeys) {
      subDisplayString = this.searchSubDisplayKeys
      .map(key => item[key])
      .filter(value => value !== null && value !== undefined && value !== '')
      .join(', ');
      if (this.itemDisabled(item)) {
        if (this.disabledItemSubStringKey && item?.[this.disabledItemSubStringKey]) {
          subDisplayString += ' (' + this.translate.instant(item[this.disabledItemSubStringKey]) + ')'
        } else {
          subDisplayString += ' (' + this.translate.instant(this.disabledTranslationKey) + ')'
        }
      }
    }
    else {
      if (this.itemDisabled(item)) {
        // HEY
        if (this.disabledItemSubStringKey && item?.[this.disabledItemSubStringKey]) {
          subDisplayString = this.translate.instant(item[this.disabledItemSubStringKey])
        } else {
          subDisplayString = this.translate.instant(this.disabledTranslationKey)
        }
      }
    }
    return subDisplayString;
  }

  setSearchResults(searchResults: { [key: string]: any }[] | null, keepDropDownOpen: boolean = false): void {
    if (!searchResults) {
      if (this.showCreateItem) {
        this.insertCreateNewItemElement();
      }
      return;
    }
    for (const item of searchResults) {
      if (!item[this.searchMainDisplayKeys[0]]) {
        item[this.searchMainDisplayKeys[0]] = '';
      }
      if (this.customNgTemplate) {
        item['ngTemplate'] = this.customNgTemplate
      }
    }
    this.searchResults = searchResults;
    for (const item in this.searchResults) {
      if (this.selectedItems.map(selectedItem => selectedItem[this.itemIdKey]).includes(this.searchResults[item][this.itemIdKey])) {
        this.searchResults[item]['__selected__'] = true
      } else {
        this.searchResults[item]['__selected__'] = false
      }
    }
    // if (this.itemIdKey) {
      // this.searchResults = this.searchResults.filter(searchResult => !this.selectedItems.map(item => item[this.itemIdKey]).includes(searchResult[this.itemIdKey]));
    // }
    // else {
      // this.searchResults = this.searchResults.filter(item => !this.selectedItems.includes(item));
    // }
    if (this.showCreateItem) {
      this.insertCreateNewItemElement();
    }
    if (!keepDropDownOpen) {
      this.showDropDown = this.searchResults.length > 0 && this.searchInput && this.searchInput.nativeElement === document.activeElement;
    }
    else {
      this.showDropDown = true;
    }
  }

  insertCreateNewItemElement(): void {
    // Remove if exists
    this.searchResults = this.searchResults.filter(item => item[this.searchMainDisplayKeys[0]] !== '__createNewItem__')

    // Insert at index 0
    this.searchResults.unshift({[this.searchMainDisplayKeys[0]]: '__createNewItem__'})
  }

  formatSearchItem(item: any): string {
    if (item) {
      if (this.addressSearch) {
        return item['display'];
      }

      let searchString = this.searchMainDisplayKeys
        .map((key, index) => {
          let valueString = item[key];
          if (this.searchDisplayCommaSeparatorAtIndices.includes(index) && valueString) {
            valueString += ',';
          }
          return valueString;
        })
        .filter(value => value !== null && value !== undefined && value !== '')
        .join(this.searchDisplaySeparator);

      if (this.searchMainParenthesisedDisplayKeys) {
        let pString = this.searchMainParenthesisedDisplayKeys
          .map(key => item[key])
          .filter(value => value !== null && value !== undefined && value !== '')
          .join(this.searchDisplaySeparator);
        if (pString) {
          searchString += ' (' + pString + ')';
        }
      }
      return searchString;
    } else {
      return '';
    }
  }

  formatSelectedItem(item: any): string {
    let keys = this.selectedMainDisplayKeys || this.searchMainDisplayKeys;
    if (item) {
      this.inputFieldClasses = this.computeInputFieldClasses();

      if (this.addressSearch) {
        let displayString = item['display'];
        if (this.showPropertyType) {
          displayString += (item['property_type_name'] && ![null, 6].includes(item['property_type_id']) ? ' (' + item['property_type_name'] + ')' : '')
        }
        return displayString
      }

      let selectedString = keys
      .map((key, index) => {
        let valueString = item[key]
        if (this.mainDisplayCommaSeparatorAtIndices.includes(index) && valueString) {
          valueString += ',';
        }
        return valueString;
      })
      .filter(value => value !== null && value !== undefined && value !== '')
      .join(this.selectedDisplaySeparator);

      const values = this.selectedMainParenthesisedDisplayKeys
        .map(key => item[key])
        .filter(value => value !== null && value !== undefined && value !== '');

      if (values.length > 0) {
          selectedString += ' (' + values.join(this.selectedDisplaySeparator) + ')';
      }

      if (!selectedString && this.fallBackDisplayKeys) {
        for (const key of this.fallBackDisplayKeys) {
          if (item[key]) {
            selectedString = item[key];
            break;
          }
        }
      }
      return selectedString;
    } else {
      return '';
    }
  }

  formatSearchEndDisplay(item: any): string {
    if (item) {
      if (this.searchEndDisplayIsAmount) {
        let unitAbbreviation = item.hasOwnProperty('unit_abbreviation') ? item['unit_abbreviation'] : null;
        return 'kr ' + currencyFormat(item[this.searchEndDisplayKey]) + (unitAbbreviation ? ' per ' + unitAbbreviation : '');
      } else {
        return item[this.searchEndDisplayKey];
      }
    } else {
      return '';
    }
  }

  formatSelectedEndDisplay(item: any): string {
    let endString = ''
    if (item) {
      let key = this.selectedEndDisplayKey || this.searchEndDisplayKey;
      if (this.selectedEndDisplayIsAmount) {
        let unitAbbreviation = item.hasOwnProperty('unit_abbreviation') ? item['unit_abbreviation'] : null;
        endString = 'kr ' + currencyFormat(item[key]) + (unitAbbreviation ? ' per ' + unitAbbreviation : '');
      } else {
        endString = item[key];
      }
      if (this.selectedEndDisplayInParentheses) {
        endString = '(' + endString + ')';
      }
    }
    return endString
  }

  getIconClass(keyNumber: number): string | null {
    const icon = this.icons[keyNumber];
    if (icon) {
      return `fa-regular ${icon}`;
    }
    return null;
  }

  getInitials(item: any): string {
    let initials = ''
    if (item) {
      if (this.initialsKeys.length > 1) {
        let firstName = item[this.initialsKeys[0]];
        let lastName = item[this.initialsKeys[1]];
        firstName = firstName.trim();
        lastName = lastName.trim();
        initials = `${firstName[0]}${lastName[0]}`
        if (initials){
          initials = initials.toUpperCase()
        }
      } else if (this.initialsKeys.length == 1) {
        let displayName = item[this.initialsKeys[0]];
        displayName = displayName.trim();
        displayName = displayName.replace('  ', ' ')
        if (displayName.includes(' ')) {
          let [firstName, lastName] = displayName.split(' ');
          initials = `${firstName[0]}${lastName[0]}`;
          if (initials){
            initials = initials.toUpperCase()
          }
        } else {
          initials = displayName[0];
          if (initials){
            initials = initials.toUpperCase()
          }
          if (displayName.length > 1) {
            initials += displayName[1];
            if (initials){
              initials = initials.toUpperCase()
            }
          }
        }
      }
    }
    return initials
  }

  itemDisabled(item: any): boolean {
    if (this.itemIdKey) {
      return this.disabledSearchResults.map(item => item[this.itemIdKey]).includes(item[this.itemIdKey]);
    } else {
        return this.disabledSearchResults.includes(item);
    }
  }

  activateDropDown() {
    if (this.searchOnFocus && this.searchable && this.searchFunction) {
      this.search()
    } else {
      this.showDropDown = !this.showDropDown;
      if (this.dropDownWidthPx === null) {
        let delta = 25;
        let maxWidth = 0;
        this.listItemDisplayNameSpans.forEach(span => {
          if (span.nativeElement.offsetWidth > maxWidth) {
            maxWidth = span.nativeElement.offsetWidth;
          }
        });
        if (maxWidth + delta > this.searchInput.nativeElement.offsetWidth) {
          this.dropDownWidthPx = maxWidth + delta;
        }
      }
    }
  }

}
