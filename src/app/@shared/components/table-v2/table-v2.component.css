.table-scroll-container {
  overflow-x: auto;
  white-space: nowrap;
  border-right: 1px solid #EBEEF1;
  border-left: 1px solid #EBEEF1;
  background-color: white;
}

.table-responsive {
  display: table;
  width: auto;
  border-collapse: separate;
  border-spacing: 0;
}

.table-header,
.table-row {
  display: table-row;
}

.table-row:hover {
  background-color: #f5f5f5 !important;
  cursor: pointer;
}

.table-cell {
  display: table-cell;
  padding: 8px 10px;
  border-top: 1px solid #EBEEF1;
  box-sizing: border-box;
  text-align: left;
  white-space: nowrap;
  overflow: visible;

}

.table-header .table-cell {
  font-weight: bold;
  color: #333;
  padding: 8px 10px;
  font-size: 13px;
  background-color: #fcfcfc;
  transition: background-color 0.3s ease;
  cursor: pointer;
}


.table-header .table-cell:hover {
  background-color: #f5f5f5;

}

.table-row:last-child .table-cell {
  border-bottom: 1px solid #EBEEF1;
}

.table-row:hover {
  background-color: #f5f5f5;
  cursor: pointer;
}

.table-header:hover {
  background-color: inherit;
  cursor: default;
}

.spinner-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}

.spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.table-cell.resizable {
  position: relative;
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
  padding-right: 20px;
}

.resizer {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 5px;
  cursor: col-resize;
  user-select: none;
  background-color: transparent;
}

.resizer:hover {
  background-color: #ccc;
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
  0 8px 10px 1px rgba(0, 0, 0, 0.14),
  0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.table-responsive.cdk-drop-list-dragging .table-cell:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.column-toggle {
  margin-left: 8px;
}


.column-toggle input[type="checkbox"] {
  cursor: pointer;
  width: 16px;
  height: 16px;
}

.all-columns-list {
  margin-top: 10px;
}

.column-item {
  cursor: pointer;
  margin-bottom: 5px;
}

.hidden-column {
  color: blue;
  text-decoration: underline;
}

.hidden-column:hover {
  color: darkblue;
}

.header-cell {
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.header-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.column-icon {
  display: none;
  margin-left: 8px;
}

.header-cell:hover .column-icon {
  display: inline-block;
}


.customer-chevron {
  margin-left: 8px;
}

.cell-content {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.customer-tooltip {
  position: fixed;
  padding: 10px;
  min-height: 100px;
  min-width: 200px;
  background-color: white;
  justify-content: center;
  font-size: 16px;
  border-radius: 10px;
  border: 1px solid #e5e8ec;
  z-index: 1000;
  pointer-events: auto;
  box-shadow: #e5e5e5 0 5px 8px ;
}

.debug-square button {
/*  margin-top: 10px;*/
/*  padding: 8px 12px;*/
/*  background-color: #d6dbe1;*/
/*  color: white;*/
/*  border: none;*/
  border-radius: 10px;
/*  cursor: pointer;*/
}

.table-loader{
  border: 1px solid #ebeef1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #FFFFFF;
}

.table-font-size {
  font-size: 14px;
}

.table-cell.customer-column-hover {
  transition: background-color 0.3s ease;
}

.table-cell.customer-column-hover:hover {
  background-color: #ececec;
}

.no-data-row{
  border: 1px solid #ebeef1;
}

orders-table-container{
  max-width:2000px;
}

/* Table row link styles */
.table-row-link {
  display: contents;
  color: inherit;
}

.table-row-link:hover {
  color: inherit;
  text-decoration: none;
}

.table-row-link:visited {
  color: inherit;
}

.table-row-link:focus {
  color: inherit;
  text-decoration: none;
}
