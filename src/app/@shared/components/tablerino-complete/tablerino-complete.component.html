<app-tablerino-header
  *ngIf="showHeader"
  [tableName]="tableName"
  [headerFiltersContainerSubject]="headerFiltersContainerSubject"
  [columnsSubject]="columnsSubject"
  [actionButtonsSubject]="actionButtonsSubject"
  [showAllFilter]="showAllFilter"
  [fetchFiltersFromUrl]="fetchFiltersFromUrl"
  [showQuickSearch]="showQuickSearch"
  [showSavedViews]="showSavedViews"
  [quickSearchInProgress]="quickSearchInProgress"
  [isLoading]="loading"
  [showColumnDropdown]="showColumnDropdown"
  [resetFiltersOnQuickSearch]="resetFiltersOnQuickSearch"
  [paginationSubject]="paginationSubject"
  (quickSearchEmitter)="quickSearchEmitter.emit($event)"
  (actionButtonEmitter)="actionButtonEmitter.emit($event)"
/>

<app-tablerino
  [disableDrag]="disableDrag"
  [disableSort]="disableSort"
  [tableName]="tableName"
  [columnsSubject]="columnsSubject"
  [tableData]="tableData"
  [settings]="settings"
  [loading]="loading"
  [noResultsTranslationKey]="noResultsTranslationKey"
  [selectedRowsSubject]="selectedRowsSubject"
  (rowClickedEmitter)="rowClickedEmitter.emit($event)"
  (selectedRowsChangedEmitter)="selectedRowsChangedEmitter.emit($event)"
  (favouriteToggledEmitter)="favouriteToggledEmitter.emit($event)"
  (toggleEmitter)="toggleEmitter.emit($event)"
  (sortEmitter)="sortEmitter.emit($event)"
></app-tablerino>

<app-table-footer
  *ngIf="showFooter && !loading"
  [paginationSubject]="paginationSubject"
  [fetchFiltersFromUrl]="fetchFiltersFromUrl"
></app-table-footer>
