.row-header{
  border: 1px solid #ebeef1;
  padding:0.5rem;
  border-bottom:none!important;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  background: #FCFCFC;
}
.header-border{
  border: solid #EBEEF1!important;
}
.btn-filter{
  border: 1px solid #E1E4E8;
  border-radius: 5px;
  padding: 0.1rem 0.5rem;
  font-weight: bold;
  height: 32px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;

}
.btn-filter.active{
  background-color: #E1E4E8;
  color: #2C2C2C;
  opacity: 1;
}
ul.drop-down > li.dropdown-item {
  cursor: pointer!important;
}

button.dropdown-toggle.btn-sort::after {
  display: none;
}

.w-30{
  width: 30px;
}
li.dropdown-item{
  padding: 0.1rem 0.75rem;
  cursor: pointer;
}

/* Active view item styling with darker background */
li.dropdown-item.active-view-item {
  background-color: #E1E4E8 !important;
  border-radius: 5px;
  /*color: #ffffff !important;*/
}

li.dropdown-item.active-view-item:hover {
  /*background-color: #c0c3c6 !important;*/
  /*color: #ffffff !important;*/
}

li.dropdown-item.active-view-item span {
  /*color: #ffffff !important;*/
}

li.dropdown-item.active-view-item i {
  /*color: #ffffff !important;*/
}
li.dropdown-divider{
  margin-top:0.25rem;
  margin-bottom:0.25rem;
}

.date-range-form {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.form-group {
  display: flex;
  flex-direction: column;
}

label {
  font-weight: bold;
  margin-bottom: 5px;
  color: #333;
}

input {
  padding: 8px;
  border: 1px solid #ccc;
  border-radius: 4px;
}

.custom-checkbox:checked {
  background-color: #4f4f4f;
  border-color: #f6f7fb;
}

/* Hide delete button in saved views dropdown by default */
.dropdown-menu .dropdown-item .fa-regular.fa-x {
  opacity: 0;
}

/* Show delete button on hover of parent dropdown item in saved views */
.dropdown-menu .dropdown-item:hover .fa-regular.fa-x {
  opacity: 1;
}

