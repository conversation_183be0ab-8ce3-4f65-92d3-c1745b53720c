<div class="row-header">
  <div class="d-flex align-items-center">
    <div class="d-flex flex-column flex-sm-row justify-content-sm-start flex-sm-wrap">
      <!-- "All" filter button - always visible -->
      <button class="btn btn-filter me-sm-2 mb-1 mb-sm-0 ms-sm-1"
              [ngClass]="{'active': isAllFilterActive, 'btn-outline-light' : !isAllFilterActive}"
              [attr.aria-pressed]="isAllFilterActive"
              (click)="toggleAllFilterBtn()">
        {{ "orders.all" | translate }}
      </button>

      <!-- My Views dropdown -->
      <ng-container *ngIf="showSavedViews && !creatingNewView">
        <div class="btn-group me-sm-2 mb-1 mb-sm-0">
          <button type="button"
                  class="btn btn-filter dropdown-toggle"
                  [ngClass]="activeViewId ? 'between-color-bg text-white' : 'btn-outline-light'"
                  data-bs-toggle="dropdown"
                  aria-expanded="false">
            <i class="fa-regular fa-table-layout me-1"></i>
            <span *ngIf="!activeViewId">{{ 'tablerino.myViews' | translate }}</span>
            <span *ngIf="activeViewId">{{ getActiveViewName() }}</span>
          </button>
          <ul class="dropdown-menu">
            <!-- Header -->
            <!-- List of saved views -->
            <div class="px-1">
              <li *ngFor="let view of savedViews" class="dropdown-item d-flex justify-content-between align-items-center"
                  [ngClass]="{'active-view-item': activeViewId === view.entry_id}"
                  (click)="applyView(view)" style="cursor: pointer;">
                <span [ngClass]="{'fw-bold': activeViewId === view.entry_id}">{{ view.table_config.name }}</span>
                <div class="d-flex">
                  <i class="fa-regular fa-x ps-1" (click)="deleteView(view.entry_id, $event)"></i>
                </div>
              </li>
            </div>

            <li *ngIf="savedViews.length === 0" class="dropdown-item text-muted">{{ 'tablerino.noSavedViews' | translate }}</li>

            <li><hr class="dropdown-divider my-1"></li>

            <!-- Add new view button -->
            <li class="dropdown-item" (click)="startCreatingNewView()" style="cursor: pointer;">
              <!--              <i class="fa-solid fa-plus me-2"></i>-->
              {{ 'tablerino.addView' | translate }}
            </li>
          </ul>
        </div>
      </ng-container>



      <!-- Always show filters -->
      <ng-container>

        <!--        &lt;!&ndash; "All" filter button - always visible &ndash;&gt;-->
        <!--        <button class="btn btn-filter me-sm-2 mb-1 mb-sm-0 ms-sm-1"-->
        <!--                [ngClass]="{'active': isAllFilterActive, 'btn-outline-light' : !isAllFilterActive}"-->
        <!--                [attr.aria-pressed]="isAllFilterActive"-->
        <!--                (click)="toggleAllFilterBtn()">-->
        <!--          {{ "orders.all" | translate }}-->
        <!--        </button>-->

        <div *ngFor="let filter of headerFilters">

          <!--  Dropdown filter   -->
          <div *ngIf="filter.dropDownOptions" class="btn-group  me-sm-2 mb-1 mb-sm-0">
            <button type="button"
                    class="btn btn-filter btn-outline-light dropdown-toggle"
                    [attr.aria-pressed]="filter.active"
                    [ngClass]="{'active': filter.active, 'btn-outline-light' : !filter.active}"
                    data-bs-toggle="dropdown"
                    aria-expanded="false">
              {{filter.translationKey | translate }}
              <span class="caret"></span>
            </button>
            <ul class="dropdown-menu">
              <!-- Dropdown options -->
              <li class="dropdown-item" *ngFor="let option of filter.dropDownOptions" (click)="onDropDownOptionSelected(filter, option, $event)">
                <input class="form-check-input me-1 cursor-pointer" [type]="filter.multiSelect ? 'checkbox' : 'radio'" [checked]="option.active">
                <span>{{ option.translationKey | translate }}</span>
              </li>
              <li class="dropdown-item text-link" (click)="!isAllFilterActive ? clearDropDownFilter(filter) : null">
                <span class="between-color" style="font-size:13px;cursor:pointer;">{{ "orders.clear" | translate }}</span>
              </li>
            </ul>
          </div>

          <!-- Button filter -->
          <button *ngIf="!filter.dropDownOptions && !filter.dateRange" class="btn btn-filter  me-sm-2 mb-1 mb-sm-0"
                  [ngClass]="{'active': filter.active, 'btn-outline-light' : !filter.active}"
                  [attr.aria-pressed]="filter.active"
                  (click)="toggleButtonFilter(filter)">
            {{ filter.translationKey | translate }}
          </button>

          <!-- Date range filter -->
          <div class="btn-group me-sm-2 mb-1 mb-sm-0" *ngIf="filter.dateRange">
            <button type="button"
                    class="btn btn-filter btn-outline-light dropdown-toggle"
                    [attr.aria-pressed]="filter.active"
                    [ngClass]="{'active': filter.active, 'btn-outline-light' : !filter.active}"
                    data-bs-toggle="dropdown"
                    aria-expanded="false">
              {{ !filter.active ? (filter.translationKey | translate) : filter.selectedDateRangeText! | translate }}
              <span class="caret"></span>
            </button>
            <ul class="dropdown-menu">
              <!-- Date range filter options -->
              <li class="dropdown-item" (click)="selectDateRange(range.key, filter, $event)" *ngFor="let range of dateRanges" [ngClass]="{'disabled' : filter.selectedDateRangeKey === range.key && filter.active}">
                <input class="form-check-input me-1" type="radio" [checked]="filter.selectedDateRangeKey === range.key">
                {{ range.translationKey | translate }}
              </li>
              <li class="dropdown-item" (click)="selectDateRange('custom', filter, $event)" [ngClass]="{'disabled' : filter.customDateRange}">
                <input class="form-check-input me-1" type="radio" [checked]="filter.customDateRange">
                {{ 'tablerino.dateRange.custom' | translate }}
              </li>
              <li *ngIf="filter.customDateRange">
                <div class="p-2" *ngIf="filter.customDateRange">
                  <div class="date-range-form">
                    <div class="form-group">
                      <label class="col-form-label-sm">{{ 'orders.starting' | translate }}:</label>
                      <input type="date" class="form-control form-control-sm" [formControl]="filter.dateRangeFromControl!" [ngClass]="{'is-invalid' : filter.dateRangeFromControl!.invalid && filter.dateRangeFromControl?.touched}">
                    </div>
                    <div class="form-group">
                      <label class="col-form-label-sm">{{ 'orders.ending' | translate }}:</label>
                      <input type="date" class="form-control form-control-sm" [formControl]="filter.dateRangeToControl!" [ngClass]="{'is-invalid' : filter.dateRangeToControl!.invalid && filter.dateRangeToControl?.touched}">
                    </div>
                  </div>
                </div>
              </li>
              <li class="dropdown-item text-link d-flex justify-content-between">
                <span class="between-color" style="font-size:13px;cursor:pointer;" (click)="!isAllFilterActive ? clearDateRangeFilter(filter) : null">{{ "orders.clear" | translate }}</span>
                <span class="between-color" style="font-size:13px;cursor:pointer;" (click)="applyCustomDateRange(filter, $event)" *ngIf="filter.customDateRange && filter.dateRangeFromControl?.valid && filter.dateRangeToControl?.valid" >{{ "orders.apply" | translate }}</span>
              </li>
            </ul>
          </div>

        </div>
      </ng-container>
    </div>
    <div *ngIf="creatingNewView || !showSavedViews || savedViews.length === 0" class="ps-2" style="border-left: 2px solid #dee2e6;">
      <!-- Save button when creating a new view -->
      <button *ngIf="creatingNewView"
              class="btn btn-filter me-sm-2 mb-1 mb-sm-0 between-color-bg text-white"
              (click)="openSaveViewDialog()">
        <i class="fa-regular fa-floppy-disk me-1"></i>
        {{ 'tablerino.saveView' | translate }}
      </button>

      <!-- Cancel button when creating a new view -->
      <button *ngIf="creatingNewView"
              class="btn btn-filter me-sm-2 mb-1 mb-sm-0"
              (click)="cancelCreatingNewView()">
        <i class="fa-solid fa-times grey-text me-1"></i>
        {{ 'common.cancel' | translate }}
      </button>
    </div>
    <div id="buttonsDiv" class="col mt-sm-0 mt-2">
      <div class="row me-1">
        <div class="col-12 p-0">
          <div class="d-flex justify-content-end gap-1">

            <!-- Custom action buttons -->
            <div *ngIf="actionButtons.length > 0 && !quickSearchActive" class="d-flex gap-1">
              <button *ngFor="let actionButton of actionButtons" [disabled]="actionButton.disabled" type="button" class="btn btn-filter btn-icon" (click)="actionButtonPressed(actionButton)">
                <i *ngIf="actionButton.iconClass && actionButton.iconPlacement != 'right'" [class]="actionButton.iconClass" class="grey-text"></i>
                <span class="grey-text" style="white-space: nowrap">{{ actionButton.translationKey | translate }}</span>
                <i *ngIf="actionButton.iconClass && actionButton.iconPlacement == 'right'" [class]="actionButton.iconClass" class="grey-text"></i>
              </button>
            </div>

            <!--            &lt;!&ndash; Show "Save View" button when no views exist &ndash;&gt;-->
            <!--            <ng-container *ngIf="showSavedViews && savedViews.length === 0 && !creatingNewView">-->
            <!--              <button class="btn btn-filter between-color-bg text-white me-sm-2 mb-1 mb-sm-0" (click)="openSaveViewDialog()">-->
            <!--                <i class="fa-regular fa-floppy-disk me-1"></i>-->
            <!--                {{ 'tablerino.saveView' | translate }}-->
            <!--              </button>-->
            <!--            </ng-container>-->

<!--            <ng-container *ngIf="creatingNewView || !showSavedViews || savedViews.length === 0">-->
<!--              &lt;!&ndash; Cancel button when creating a new view &ndash;&gt;-->
<!--              <button *ngIf="creatingNewView"-->
<!--                      class="btn btn-filter me-sm-2 mb-1 mb-sm-0"-->
<!--                      (click)="cancelCreatingNewView()">-->
<!--                <i class="fa-solid fa-times grey-text me-1"></i>-->
<!--                {{ 'common.cancel' | translate }}-->
<!--              </button>-->

<!--              &lt;!&ndash; Save button when creating a new view &ndash;&gt;-->
<!--              <button *ngIf="creatingNewView"-->
<!--                      class="btn btn-filter me-sm-2 mb-1 mb-sm-0 between-color-bg text-white"-->
<!--                      (click)="openSaveViewDialog()">-->
<!--                <i class="fa-regular fa-floppy-disk me-1"></i>-->
<!--                {{ 'tablerino.saveView' | translate }}-->
<!--              </button>-->
<!--            </ng-container>-->


            <!-- Search button -->
            <button *ngIf="!quickSearchActive && showQuickSearch" type="button" class="btn btn-filter btn-icon" (click)="toggleQuickSearch()">
              <i class="fa-solid fa-magnifying-glass"></i>
            </button>

            <!-- Search field with clear icon -->
            <div *ngIf="quickSearchActive" class="position-relative" style="width:300px;margin-left: auto;">
              <input
                id="searchField"
                type="text"
                maxlength="200"
                class="form-control input-search"
                style="height: 27px; padding-right: 30px;"
                placeholder="{{'orders.searchField' | translate}}"
                [formControl]="quickSearchControl"
                tabindex="0"
                (input)="onEnterPressed($event)"
                #searchInput
              />
              <!-- Clear button inside the search field - always visible -->
              <button
                type="button"
                class="btn btn-sm position-absolute"
                style="right: 0; top: 0; height: 100%; border: none; background: transparent;"
                (click)="clearSearch($event)"
              >
                <i class="fa-solid fa-times"></i>
              </button>
            </div>

            <!-- Column dropdown button -->
            <div *ngIf="showColumnDropdown && !quickSearchActive" class="mb-1 mb-sm-0 ">
              <button class="btn btn-filter btn-outline-light dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" data-bs-auto-close="outside" style="cursor: pointer;">
                <i class="fa-regular fa-columns-3 grey-text"></i>
              </button>
              <ul class="dropdown-menu">
                <!-- Unified Column Toggles -->
                <li class="dropdown-item" *ngFor="let column of columns" (click)="toggleColumnVisibility(column)">
                  <div *ngIf="!column.buttonColumn" class="d-flex align-items-center">
                    <input type="checkbox" [checked]="column.visible" class="form-check-input me-1 custom-checkbox" />
                    <div>{{ column.labelKey! | translate }}</div>
                  </div>
                </li>
                <li class="clickable-text font-12 mt-1" style="padding-left: 12px; color: var(--primary-color)" (click)="resetColumns()">{{'orders.reset' | translate}}</li>
              </ul>
            </div>

            <!--            &lt;!&ndash; Save view button &ndash;&gt;-->
            <!--&lt;!&ndash;            <div *ngIf="showSaveViewButton && !quickSearchActive" class="mb-1 mb-sm-0">&ndash;&gt;-->
            <!--              <div *ngIf=" !quickSearchActive && showSavedViews" class="mb-1 mb-sm-0">-->

            <!--              <button class="btn btn-filter btn-outline-light dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" data-bs-auto-close="outside" style="cursor: pointer;">-->
            <!--                <i class="fa-regular fa-bookmark grey-text"></i>-->
            <!--              </button>-->
            <!--              <ul class="dropdown-menu">-->
            <!--                <li class="dropdown-item fw-bold">{{ 'tablerino.savedViews' | translate }}</li>-->

            <!--                &lt;!&ndash; List of saved views &ndash;&gt;-->
            <!--                <li *ngFor="let view of savedViews" class="dropdown-item d-flex justify-content-between align-items-center" (click)="applyView(view)" style="cursor: pointer;">-->
            <!--                  <span>{{ view.table_config.name }}</span>-->
            <!--                  <div class="d-flex">-->
            <!--                    <i class="fa-regular fa-trash-can clickable-text" (click)="deleteView(view.entry_id, $event)"></i>-->
            <!--                  </div>-->
            <!--                </li>-->

            <!--                <li *ngIf="savedViews.length === 0" class="dropdown-item text-muted">{{ 'tablerino.noSavedViews' | translate }}</li>-->

            <!--                <li><hr class="dropdown-divider"></li>-->

            <!--                &lt;!&ndash; Save current view button &ndash;&gt;-->
            <!--                <li class="dropdown-item">-->
            <!--                  <button class="btn btn-sm btn-primary w-100" (click)="openSaveViewDialog()">-->
            <!--                    <i class="fa-regular fa-floppy-disk me-1"></i>-->
            <!--                    {{ 'tablerino.saveCurrentView' | translate }}-->
            <!--                  </button>-->
            <!--                </li>-->
            <!--              </ul>-->
            <!--            </div>-->

            <!-- Dropdown for custom action buttons -->
            <div *ngIf="dropDownActionButtons.length > 0 && !quickSearchActive" class="btn-group">
              <div class="dropdown">
                <button
                  class="btn btn-filter btn-outline-light dropdown-toggle text-white between-color-bg"
                  type="button"
                  id="dropdownMenuButton"
                  data-bs-toggle="dropdown"
                  aria-expanded="false"
                >
                  <span class="text-white">{{ "orders.actions" | translate }}</span>
                </button>
                <div class="dropdown-menu" aria-labelledby="dropdownMenuButton" style="cursor:pointer;">
                  <button *ngFor="let actionButton of dropDownActionButtons" [disabled]="actionButton.disabled" class="dropdown-item" (click)="actionButtonPressed(actionButton)">
                    <i *ngIf="actionButton.iconClass && actionButton.iconPlacement != 'right'" [class]="actionButton.iconClass" [ngClass]="{'text-muted': actionButton.disabled}"></i>
                    <span [ngClass]="{'text-muted': actionButton.disabled}">{{ actionButton.translationKey | translate }}</span>
                    <i *ngIf="actionButton.iconClass && actionButton.iconPlacement == 'right'" [class]="actionButton.iconClass" [ngClass]="{'text-muted': actionButton.disabled}"></i>
                  </button>
                </div>
              </div>
            </div>

          </div>

        </div>
      </div>
    </div>
  </div>
</div>
