import {Compo<PERSON>, ElementRef, EventE<PERSON>ter, Input, OnDestroy, OnInit, Output, Renderer2, ViewChild} from '@angular/core';
import {FormControl} from "@angular/forms";
import {NgbModalRef, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {Router, ActivatedRoute} from "@angular/router";
import moment from "moment/moment";

import {TranslateService} from "@ngx-translate/core";
import {BehaviorSubject, Subject, Subscription, debounceTime, distinctUntilChanged} from "rxjs";
import {TablerinoColumn} from "../tablerino/tablerino.component";
import {SharedService} from "../../services/shared.service";
import {PaginationContainer} from "../../models/global.interfaces";
import {StandardImports} from "../../global_import";
import {ToastService} from "../../../@core/services/toast.service";
import { SaveViewModalComponent } from '../save-view-modal/save-view-modal.component';
import {VerifyPopupModal} from "../verify-popup-modal/verify-popup-modal";

// Container for header filters. As long as init is true, update to the filters will not trigger a request.
// This is to prevent multiple requests on initialization.
// Init will be set to false as soon as any filters are activated from this component.
export interface HeaderFiltersContainer {
  filters: HeaderFilterComponent[];
  init: boolean;
}

// Header filter component, represents a single filter element in the header
export interface HeaderFilterComponent {
  parameterName: string;
  translationKey: string;
  dropDownOptions?: HeaderFilterDropDownOption[] | null;
  multiSelect?: boolean;
  dateRange?: boolean;
  active: boolean;
  dateRangeFromControl?: FormControl;
  dateRangeToControl?: FormControl;
  dateRangeFromParamKey?: string;
  dateRangeToParamKey?: string;
  selectedDateRangeText?: string;
  selectedDateRangeKey?: string;
  customDateRange?: boolean;
  excludes?: string[];
}

// Header filter drop down option, represents a single option available within a dropdown defined in the header filter component
export interface HeaderFilterDropDownOption {
  value: string | number,
  translationKey: string,
  active: boolean
}

// Sort option
export interface SortOption {
  value: string;
  active: boolean;
  translationKey: string;
}

// Sorting container
export interface SortContainer {
  sortOptions: SortOption[];
  sortDirection: 'asc' | 'desc';
}

// Custom action button
export interface CustomActionButton {
  functionName: string;
  translationKey: string;
  iconClass?: string;
  iconPlacement?: 'left' | 'right';
  disabled?: boolean;
  placeInDropdown?: boolean;
}

// Add these interfaces to your component file or move them to a shared models file
interface SavedFilterState {
  filters: SavedFilter[];
  search?: string;
  sorting?: SavedSortingState;
}

interface SavedSortingState {
  columnName: string;
  direction: 'asc' | 'desc';
}

interface SavedFilter {
  parameterName: string;
  translationKey: string;
  active: boolean;
  dropDownOptions?: SavedFilterDropDownOption[];
  dateRange?: SavedFilterDateRange;
  multiSelect?: boolean;
  excludes?: string[];
}

interface SavedFilterDropDownOption {
  value: string | number;
  translationKey: string;
  active: boolean;
}

interface SavedFilterDateRange {
  from: string;
  to: string;
  selectedDateRangeKey?: string;
  customDateRange?: boolean;
}

@Component({
  selector: 'app-tablerino-header',
  templateUrl: './tablerino-header.component.html',
  styleUrls: ['./tablerino-header.component.css'],
  standalone: true,
  imports: [StandardImports]
})

export class TablerinoHeaderComponent implements OnInit, OnDestroy {
  @Input() headerFiltersContainerSubject: Subject<HeaderFiltersContainer> = new Subject<HeaderFiltersContainer>();
  @Input() tableName: string;
  headerFilters: HeaderFilterComponent[] = [];
  creatingNewView: boolean = false;
  activeViewId: number | null = null;
  originalViewState: SavedFilterState | null = null;
  private isApplyingView: boolean = false;

  @Input() showSavedViews: boolean = false;
  @Input() showAllFilter: boolean = false;
  @Input() fetchFiltersFromUrl: boolean = false;
  @Input() showQuickSearch: boolean = false;

  @Input() quickSearchInProgress: boolean = false;
  @Input() isLoading: boolean = false;

  @Input() columnsSubject: BehaviorSubject<TablerinoColumn[]>;
  @Input() showColumnDropdown: boolean = true;
  @Input() resetFiltersOnQuickSearch: boolean = false;
  @Input() actionButtonsSubject: BehaviorSubject<CustomActionButton[]>;

  @Input() paginationSubject: BehaviorSubject<PaginationContainer>;

  @Output() actionButtonEmitter = new EventEmitter<string>();
  @Output() quickSearchEmitter = new EventEmitter<string>();

  @ViewChild('searchInput') searchInput: ElementRef;

  columns: TablerinoColumn[] = [];

  quickSearchControl: FormControl = new FormControl();
  quickSearchActive = false;
  private searchSubscription: Subscription;
  private searchDebounceTime = 200; // Debounce time in milliseconds
  private documentClickUnlisten: () => void;
  private searchFieldKeydownUnlisten: () => void;

  modalReference: NgbModalRef;
  isAllFilterActive : boolean = false;
  filtersModified: boolean = false;

  sortOptions: SortOption[] = [];
  sortDirection: 'asc' | 'desc' = 'desc';
  isSortingActive: boolean = false;

  actionButtons: CustomActionButton[] = [];
  dropDownActionButtons: CustomActionButton[] = [];

  savedViews: any[] = [];
  newViewName: string = '';
  showSaveViewDialog: boolean = false;
  editingViewId: number | null = null;

  //date range array of objects
  dateRanges = [
    {key: 'next30Days', translationKey: 'tablerino.dateRange.next30Days'},
    {key: 'next7Days', translationKey: 'tablerino.dateRange.next7Days'},
    {key: 'today', translationKey: 'tablerino.dateRange.today'},
    {key: 'last7Days', translationKey: 'tablerino.dateRange.last7Days'},
    {key: 'last30Days', translationKey: 'tablerino.dateRange.last30Days'},
  ]

  constructor(
    private renderer: Renderer2,
    private el: ElementRef,
    private translateService: TranslateService,
    private activatedRoute: ActivatedRoute,
    private sharedService: SharedService,
    private router: Router,
    private toastService: ToastService,
    private modalService: NgbModal
  ) {
  }

  ngOnInit() {
    if (!this.tableName) {
      console.error('Table name is required');
    }

    if (this.tableName) {
      this.loadSavedViews();
    }

    // Set up debounced search
    this.setupDebouncedSearch();

    // Set up URL parameter change detection
    this.setupUrlParamChangeDetection();

    // Fetch user table columns if they exist
    this.sharedService.getUserTableColumns(this.tableName).subscribe(columns => {
      if (columns.length > 0) {
        let subjectColumns = this.columnsSubject.value;
        subjectColumns.forEach(subjectColumn => {
          subjectColumn.visible = !!(columns.includes(subjectColumn.name) || subjectColumn.buttonColumn);
        });
        subjectColumns = subjectColumns.sort((a, b) => {
          if (a.buttonColumn && !b.buttonColumn) return 1;   // a goes after b
          if (!a.buttonColumn && b.buttonColumn) return -1;  // a goes before b
          // Otherwise, sort by index in columns
          return columns.indexOf(a.name) - columns.indexOf(b.name);
        });

        this.columnsSubject.next(subjectColumns);
      }
    });

    // Subscribe to filter container changes
    if (this.headerFiltersContainerSubject) {
      this.headerFiltersContainerSubject.subscribe((container) => {
        this.headerFilters = container.filters;

        // If filters are initialized and URL filtering is enabled, load filters from URL
        if (this.headerFilters.length > 0 && this.fetchFiltersFromUrl && container.init) {
          // Wait for the next tick to ensure all filters are properly initialized
          setTimeout(() => {
            this.loadFilterOptionsFromUrl();
          });
        }
      });
    }

    // Subscribe to column changes
    if (this.columnsSubject) {
      this.columnsSubject.subscribe((columns) => {
        const previousColumns = this.columns;
        this.columns = [...columns].sort((a, b) => {
          return this.translateService.instant(a.labelKey).localeCompare(this.translateService.instant(b.labelKey));
        });

        // Check if sorting has changed and update URL if needed
        if (previousColumns.length > 0 && this.fetchFiltersFromUrl) {
          const previousSortedColumn = previousColumns.find(col => col.sortedAsc || col.sortedDesc);
          const currentSortedColumn = columns.find(col => col.sortedAsc || col.sortedDesc);

          // Check if sorting state has changed
          const sortingChanged = this.hasSortingChanged(previousSortedColumn, currentSortedColumn);

          if (sortingChanged && !this.isApplyingView) {
            this.handleSortingChange();
          }
        }

        // Check for edit mode when sorting changes (after initial load)
        // But skip this check if we're currently applying a view to prevent premature deselection
        if (this.headerFilters.length > 0 && !this.isApplyingView) {
          this.checkForEditMode();
        }
      });
    }

    // Subscribe to action button changes
    if (this.actionButtonsSubject) {
      this.actionButtonsSubject.subscribe((buttons) => {
        this.actionButtons = buttons.filter(button => !button.placeInDropdown);
        this.dropDownActionButtons = buttons.filter(button => button.placeInDropdown);
      });
    }

    // Subscribe to pagination changes
    if (this.paginationSubject) {
      this.paginationSubject.subscribe(() => {
        // Handle pagination changes if needed
      });
    }

    // If URL filtering is enabled and we already have filters, load them from URL
    if (this.fetchFiltersFromUrl && this.headerFilters.length > 0) {
      this.loadFilterOptionsFromUrl();
    }
  }

  ngOnDestroy() {
    // Clean up subscriptions to prevent memory leaks
    if (this.searchSubscription) {
      this.searchSubscription.unsubscribe();
    }

    if (this.documentClickUnlisten) {
      this.documentClickUnlisten();
    }

    if (this.searchFieldKeydownUnlisten) {
      this.searchFieldKeydownUnlisten();
    }
  }

  // Method to clear the search input and close it
  clearSearch(event: MouseEvent) {
    // Stop propagation to prevent the document click listener from firing
    event.stopPropagation();

    // Clear the search input
    this.quickSearchControl.setValue('');

    // Remove search parameter from URL and reset pagination if URL filtering is enabled
    if (this.fetchFiltersFromUrl) {
      let queryParams: { [key: string]: any } = { search: null };

      // Reset pagination to page 1 when search is cleared
      if (this.paginationSubject) {
        const paginationContainer = this.paginationSubject.value;

        // Reset page to 1 in the URL
        queryParams['page'] = 1;

        // Also update the pagination container
        if (paginationContainer.page !== 1) {
          paginationContainer.page = 1;
          this.paginationSubject.next(paginationContainer);
        }
      }

      this.updateUrlWithNavigate(queryParams);
    }

    // Emit empty search event
    this.quickSearchEmitter.emit('');

    // Close the search input
    this.quickSearchActive = false;
    this.quickSearchInProgress = false;
  }

  private setupDebouncedSearch() {
    // Only set up debounced search if quick search is enabled
    if (this.showQuickSearch) {
      // Unsubscribe from previous subscription if it exists
      if (this.searchSubscription) {
        this.searchSubscription.unsubscribe();
      }

      // Create a temporary variable to store the current search term
      let currentSearchTerm: string | null = this.quickSearchControl.value;

      // If we have an initial search term, make sure it's in the URL
      if (currentSearchTerm && this.fetchFiltersFromUrl) {
        // Use navigate to ensure the initial search term is in the URL
        this.updateUrlWithNavigate({
          search: currentSearchTerm
        });
      }

      // Create a new subscription with debounce
      this.searchSubscription = this.quickSearchControl.valueChanges
        .pipe(
          // Use a longer debounce time to reduce history entries while typing
          debounceTime(this.searchDebounceTime),
          distinctUntilChanged()
        )
        .subscribe(searchTerm => {
          // Store the search term for URL updates
          currentSearchTerm = searchTerm;

          // Check for edit mode when search changes
          this.checkForEditMode();

          if (searchTerm) {
            this.quickSearchInProgress = true;

            // Emit search event immediately (don't wait for URL update)
            this.quickSearchEmitter.emit(searchTerm);
          } else if (searchTerm === '') {
            // Emit empty search event
            this.quickSearchEmitter.emit('');
          }

          // Update URL with search term if URL filtering is enabled
          // This is done outside the if/else to ensure we only update URL once
          if (this.fetchFiltersFromUrl) {
            let queryParams: { [key: string]: any } = {
              search: currentSearchTerm || null
            };

            // Reset pagination to page 1 when search changes
            if (this.paginationSubject) {
              const paginationContainer = this.paginationSubject.value;

              // Reset page to 1 in the URL
              queryParams['page'] = 1;

              // Also update the pagination container
              if (paginationContainer.page !== 1) {
                paginationContainer.page = 1;
                this.paginationSubject.next(paginationContainer);
              }
            }

            // Use replaceState for intermediate search terms to avoid cluttering history
            this.updateUrlWithReplaceState(queryParams);
          }
        });

      // Add event listeners to handle form submissions and ensure search term is preserved
      this.documentClickUnlisten = this.renderer.listen('document', 'click', (event: MouseEvent) => {
        const target = event.target as HTMLElement;
        if (target && (
          target.tagName === 'BUTTON' ||
          (target.tagName === 'INPUT' && (target as HTMLInputElement).type === 'submit')
        )) {
          if (currentSearchTerm && this.fetchFiltersFromUrl) {
            this.updateUrlWithNavigate({
              search: currentSearchTerm
            });
          }
        }
      });

      // Also handle Enter key press to finalize the URL
      const searchField = this.el.nativeElement.querySelector('#searchField');
      if (searchField) {
        this.searchFieldKeydownUnlisten = this.renderer.listen(searchField, 'keydown', (event: KeyboardEvent) => {
          if (event.key === 'Enter' && this.fetchFiltersFromUrl && currentSearchTerm !== null) {
            this.updateUrlWithNavigate({
              search: currentSearchTerm || null
            });
          }
        });
      }
    }
  }

  // Helper method to update URL with replaceState (no history entry)
  private updateUrlWithReplaceState(queryParams: { [key: string]: any }) {
    // Get current query params
    const currentParams = { ...this.activatedRoute.snapshot.queryParams };

    // Special handling for search parameter to ensure it's preserved
    // when it's not explicitly being updated
    const hasSearchParam = Object.keys(queryParams).includes('search');
    const currentSearchParam = currentParams['search'];

    // Update with new params
    Object.keys(queryParams).forEach(key => {
      if (queryParams[key] === null || queryParams[key] === undefined || queryParams[key] === '') {
        delete currentParams[key];
      } else {
        currentParams[key] = queryParams[key];
      }
    });

    // If we're not explicitly updating the search parameter and there is a current search,
    // preserve it
    if (!hasSearchParam && currentSearchParam) {
      currentParams['search'] = currentSearchParam;
    }

    // Use Location.replaceState to update URL without creating history entry
    const url = this.router.createUrlTree([], {
      relativeTo: this.activatedRoute,
      queryParams: currentParams
    }).toString();

    // Use history.replaceState to update URL without creating a new history entry
    window.history.replaceState({}, '', url);
  }

  // Helper method to update URL with navigate (creates history entry)
  private updateUrlWithNavigate(queryParams: { [key: string]: any }) {
    // Get current query params
    const currentParams = { ...this.activatedRoute.snapshot.queryParams };

    // Special handling for search parameter to ensure it's preserved
    // when it's not explicitly being updated
    const hasSearchParam = Object.keys(queryParams).includes('search');
    const currentSearchParam = currentParams['search'];

    // Update with new params
    Object.keys(queryParams).forEach(key => {
      if (queryParams[key] === null || queryParams[key] === undefined || queryParams[key] === '') {
        delete currentParams[key];
      } else {
        currentParams[key] = queryParams[key];
      }
    });

    // If we're not explicitly updating the search parameter and there is a current search,
    // preserve it
    if (!hasSearchParam && currentSearchParam) {
      currentParams['search'] = currentSearchParam;
    }

    // Use router.navigate to update URL, creating a new history entry
    this.router.navigate([], {
      relativeTo: this.activatedRoute,
      queryParams: currentParams,
    });
  }

  // This method will be called from ngOnInit to set up URL parameter change detection
  private setupUrlParamChangeDetection() {
    if (this.fetchFiltersFromUrl) {
      // Subscribe to URL parameter changes
      this.activatedRoute.queryParams.subscribe(params => {
        // Check if we need to reload filters from URL
        let needsReload = false;

        // Check search parameter
        if (this.showQuickSearch) {
          const searchParam = params['search'];

          // Only update if the search parameter changed and doesn't match current value
          if (searchParam !== undefined && searchParam !== this.quickSearchControl.value) {
            // Update the search control without triggering valueChanges
            this.quickSearchControl.setValue(searchParam || '', { emitEvent: false });

            // If there's a search term, emit the search event
            if (searchParam) {
              this.quickSearchActive = true;
              this.quickSearchInProgress = true;
              needsReload = true;
            } else {
              this.quickSearchActive = false;
              this.quickSearchInProgress = false;
              // Only reload if we had a search term before
              needsReload = !!this.quickSearchControl.value;
            }
          }
        }

        // Check filter parameters
        let anyFilterActive = false;
        this.headerFilters.forEach(filter => {
          if (filter.dateRange) {
            // Check date range parameters
            let dateFilterChanged = false;

            if (filter.dateRangeFromParamKey) {
              const fromParam = params[filter.dateRangeFromParamKey];
              const currentValue = filter.dateRangeFromControl?.value;

              if (fromParam !== currentValue) {
                filter.dateRangeFromControl?.setValue(fromParam || null, { emitEvent: false });
                dateFilterChanged = true;
              }
            }

            if (filter.dateRangeToParamKey) {
              const toParam = params[filter.dateRangeToParamKey];
              const currentValue = filter.dateRangeToControl?.value;

              if (toParam !== currentValue) {
                filter.dateRangeToControl?.setValue(toParam || null, { emitEvent: false });
                dateFilterChanged = true;
              }
            }

            // Update filter active state
            const wasActive = filter.active;
            filter.active = !!(filter.dateRangeFromControl?.value || filter.dateRangeToControl?.value);

            if (filter.active) {
              anyFilterActive = true;

              // Update date range UI if the filter is active
              let selectedDateRangeKey = this.recogniseDateRange(
                filter.dateRangeFromControl?.value,
                filter.dateRangeToControl?.value
              );

              if (selectedDateRangeKey !== 'custom') {
                filter.selectedDateRangeKey = selectedDateRangeKey;
                filter.selectedDateRangeText = 'tablerino.dateRange.' + filter.selectedDateRangeKey;
                filter.customDateRange = false;
              } else {
                filter.customDateRange = true;
                filter.selectedDateRangeKey = 'custom';

                // Format the custom date range text
                if (filter.dateRangeFromControl?.value && filter.dateRangeToControl?.value) {
                  const startDateFormatted = new Date(filter.dateRangeFromControl.value).toLocaleDateString();
                  const endDateFormatted = new Date(filter.dateRangeToControl.value).toLocaleDateString();
                  filter.selectedDateRangeText = `${startDateFormatted} ${this.translateService.instant('common.to')} ${endDateFormatted}`;
                }
              }
            }

            // Check if we need to reload
            if (dateFilterChanged || wasActive !== filter.active) {
              needsReload = true;
            }
          } else if (filter.dropDownOptions) {
            // Check dropdown parameters
            const paramValue = params[filter.parameterName];
            const paramValues = paramValue ? paramValue.split(',').filter((val: string) => val.trim() !== '') : [];

            // Check if dropdown options have changed
            let optionsChanged = false;

            // Reset all options first
            filter.dropDownOptions.forEach(option => {
              const wasActive = option.active;
              option.active = paramValues.includes(option.value.toString());

              if (wasActive !== option.active) {
                optionsChanged = true;
              }
            });

            // Update filter active state
            const wasActive = filter.active;
            filter.active = filter.dropDownOptions.some(option => option.active);

            if (filter.active) {
              anyFilterActive = true;
            }

            // Check if we need to reload
            if (optionsChanged || wasActive !== filter.active) {
              needsReload = true;
            }
          } else {
            // Check boolean/toggle parameters
            const paramValue = params[filter.parameterName];
            const wasActive = filter.active;

            filter.active = paramValue === 'true' || paramValue === '1' || paramValue === true;

            if (filter.active) {
              anyFilterActive = true;
            }

            // Check if we need to reload
            if (wasActive !== filter.active) {
              needsReload = true;
            }
          }
        });

        // Update the "All" filter state
        this.isAllFilterActive = !anyFilterActive;

        // Check sorting parameters
        if (this.columnsSubject) {
          const currentSortedColumn = this.columnsSubject.value?.find(col => col.sortedAsc || col.sortedDesc);
          const urlSortBy = params['sortBy'];
          const urlSortOrder = params['sortOrder'];

          let sortingChanged = false;

          // Check if sorting parameters in URL differ from current state
          if (urlSortBy && urlSortOrder) {
            if (!currentSortedColumn ||
                currentSortedColumn.name !== urlSortBy ||
                (currentSortedColumn.sortedAsc && urlSortOrder !== 'asc') ||
                (currentSortedColumn.sortedDesc && urlSortOrder !== 'desc')) {
              sortingChanged = true;
            }
          } else if (currentSortedColumn) {
            // URL has no sorting but we have a sorted column
            sortingChanged = true;
          }

          if (sortingChanged) {
            this.loadSortingFromUrl(params);
            needsReload = true;
          }
        }

        // Update pagination if needed
        if (this.paginationSubject) {
          let paginationChanged = false;
          let paginationContainer = this.paginationSubject.value;

          if (params['page'] && paginationContainer.page !== parseInt(params['page'])) {
            paginationContainer.page = parseInt(params['page']);
            paginationChanged = true;
          }

          if (params['limit'] && paginationContainer.limit !== parseInt(params['limit'])) {
            paginationContainer.limit = parseInt(params['limit']);
            paginationChanged = true;
          }

          if (paginationChanged) {
            this.paginationSubject.next(paginationContainer);
            needsReload = true;
          }
        }

        // If any changes were detected, notify subscribers and trigger a reload
        if (needsReload) {
          // Notify subscribers about the filter changes
          this.headerFiltersContainerSubject.next({filters: this.headerFilters, init: false});

          // If there's a search term, emit it
          if (this.quickSearchControl.value) {
            this.quickSearchEmitter.emit(this.quickSearchControl.value);
          }
        }
      });
    }
  }

  private updateUrlWithoutNewHistoryEntry(queryParams: { [key: string]: any }) {
    // Use the navigate method to create a history entry
    this.updateUrlWithNavigate(queryParams);
  }

  // Helper method to update sorting parameters in URL
  private updateSortingInUrl(queryParams: { [key: string]: any }) {
    if (this.columnsSubject) {
      const sortedColumn = this.columnsSubject.value?.find(col => col.sortedAsc || col.sortedDesc);

      if (sortedColumn) {
        queryParams['sortBy'] = sortedColumn.name;
        queryParams['sortOrder'] = sortedColumn.sortedAsc ? 'asc' : 'desc';
      } else {
        // Remove sorting parameters if no column is sorted
        delete queryParams['sortBy'];
        delete queryParams['sortOrder'];
      }
    }
  }

  // Method to handle sorting changes and update URL
  handleSortingChange(): void {
    if (this.fetchFiltersFromUrl) {
      const newParams: Record<string, any> = {
        ...this.activatedRoute.snapshot.queryParams
      };

      // Update sorting parameters
      this.updateSortingInUrl(newParams);

      // Reset pagination to page 1 when sorting changes
      if (this.paginationSubject) {
        newParams['page'] = 1;
        const pg = this.paginationSubject.value;
        if (pg.page !== 1) {
          pg.page = 1;
          this.paginationSubject.next(pg);
        }
      }

      // Check for edit mode when sorting changes
      this.checkForEditMode();

      this.router.navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: newParams
      });
    }
  }

  // Helper method to check if sorting state has changed
  private hasSortingChanged(previousSortedColumn: TablerinoColumn | undefined, currentSortedColumn: TablerinoColumn | undefined): boolean {
    if (!previousSortedColumn && !currentSortedColumn) {
      return false;
    }

    if (!previousSortedColumn || !currentSortedColumn) {
      return true;
    }

    if (previousSortedColumn.name !== currentSortedColumn.name) {
      return true;
    }

    if ((previousSortedColumn.sortedAsc !== currentSortedColumn.sortedAsc) ||
        (previousSortedColumn.sortedDesc !== currentSortedColumn.sortedDesc)) {
      return true;
    }

    return false;
  }

  // Helper method to load sorting state from URL parameters
  private loadSortingFromUrl(params: any): void {
    if (this.columnsSubject && params['sortBy'] && params['sortOrder']) {
      const columns = this.columnsSubject.value;
      if (columns) {
        // Reset all column sorting first
        columns.forEach(col => {
          col.sortedAsc = false;
          col.sortedDesc = false;
        });

        const targetColumn = columns.find(col => col.name === params['sortBy']);
        if (targetColumn && targetColumn.sort) {
          if (params['sortOrder'] === 'asc') {
            targetColumn.sortedAsc = true;
          } else if (params['sortOrder'] === 'desc') {
            targetColumn.sortedDesc = true;
          }
        }
        this.columnsSubject.next([...columns]);
      }
    }
  }

  private loadFilterOptionsFromUrl() {
    // Get URL parameters from the current route
    let params = this.activatedRoute.snapshot.queryParams;
    let anyFilterActive = false;

    // Reset all filters first to ensure a clean state
    this.headerFilters.forEach(filter => {
      filter.active = false;

      if (filter.dropDownOptions) {
        filter.dropDownOptions.forEach(option => {
          option.active = false;
        });
      }

      if (filter.dateRangeFromControl) {
        filter.dateRangeFromControl.reset();
      }

      if (filter.dateRangeToControl) {
        filter.dateRangeToControl.reset();
      }
    });

    if (params['search'] && this.showQuickSearch) {
      this.quickSearchControl.setValue(params['search']);
      this.quickSearchActive = true;
    }

    this.headerFilters.forEach(filter => {
      if (filter.dateRange) {
        let dateFilterActive = false;

        if (filter.dateRangeFromParamKey && params[filter.dateRangeFromParamKey]) {
          filter.dateRangeFromControl?.setValue(params[filter.dateRangeFromParamKey]);
          dateFilterActive = true;
        }

        if (filter.dateRangeToParamKey && params[filter.dateRangeToParamKey]) {
          filter.dateRangeToControl?.setValue(params[filter.dateRangeToParamKey]);
          dateFilterActive = true;
        }

        filter.active = dateFilterActive;

        if (filter.active) {
          anyFilterActive = true;
          let selectedDateRangeKey = this.recogniseDateRange(filter.dateRangeFromControl?.value, filter.dateRangeToControl?.value);
          if (selectedDateRangeKey !== 'custom') {
            filter.selectedDateRangeKey = selectedDateRangeKey;
            filter.selectedDateRangeText = 'tablerino.dateRange.' + filter.selectedDateRangeKey;
            filter.customDateRange = false;
          } else {
            filter.customDateRange = true;
            filter.selectedDateRangeKey = 'custom';
            // Format the custom date range text
            if (filter.dateRangeFromControl?.value && filter.dateRangeToControl?.value) {
              const startDateFormatted = new Date(filter.dateRangeFromControl.value).toLocaleDateString();
              const endDateFormatted = new Date(filter.dateRangeToControl.value).toLocaleDateString();
              filter.selectedDateRangeText = `${startDateFormatted} ${this.translateService.instant('common.to')} ${endDateFormatted}`;
            }
          }
        }
      } else if (filter.dropDownOptions) {
        // Handle dropdown filters
        if (params[filter.parameterName]) {
          const paramValues = params[filter.parameterName].split(',').filter((val: string) => val.trim() !== '');

          if (paramValues.length > 0) {
            // Set active state for options that match URL parameters
            let hasActiveOption = false;
            filter.dropDownOptions.forEach(option => {
              if (paramValues.includes(option.value.toString())) {
                option.active = true;
                hasActiveOption = true;
              }
            });

            // Set the filter as active if any options are active
            filter.active = hasActiveOption;
            if (hasActiveOption) {
              anyFilterActive = true;
            }
          }
        }
      } else {
        // Handle boolean/toggle filters
        const paramValue = params[filter.parameterName];
        if (paramValue !== undefined && paramValue !== null && paramValue !== '') {
          filter.active = paramValue === 'true' || paramValue === '1' || paramValue === true;
          if (filter.active) {
            anyFilterActive = true;
          }
        }
      }
    });

    // Update the "All" filter state based on whether any filters are active
    this.isAllFilterActive = !anyFilterActive;

    // Update pagination if needed
    if (this.paginationSubject) {
      let paginationContainer = this.paginationSubject.value;
      if (params['page']) {
        paginationContainer.page = parseInt(params['page']);
      }
      if (params['limit']) {
        paginationContainer.limit = parseInt(params['limit']);
      }
      this.paginationSubject.next(paginationContainer);
    }

    // Load sorting parameters from URL
    this.loadSortingFromUrl(params);

    // Update the filter container with the loaded filters
    this.headerFiltersContainerSubject.next({filters: this.headerFilters, init: false});

    // Emit search term if it exists in URL
    if (params['search'] && this.showQuickSearch) {
      // Use setTimeout to ensure the component is fully initialized before emitting the search
      setTimeout(() => {
        this.quickSearchEmitter.emit(params['search']);
      });
    }
  }



  toggleQuickSearch() {
    this.quickSearchActive = !this.quickSearchActive;
    if (this.quickSearchActive) {
      // Focus the search input after it's rendered
      setTimeout(() => {
        const searchField = this.el.nativeElement.querySelector('#searchField');
        if (searchField) {
          this.renderer.selectRootElement(searchField).focus();
        }
      });
    } else {
      // If we're closing the search and have a search term, update the URL
      if (this.quickSearchControl.value && this.fetchFiltersFromUrl) {
        this.updateUrlWithNavigate({
          search: this.quickSearchControl.value
        });
      }
    }
  }

  actionButtonPressed(button: CustomActionButton) {
    this.actionButtonEmitter.emit(button.functionName);
  }

  toggleAllFilterBtn() {
    this.activeViewId = null;
    this.isAllFilterActive = true;

    // Clear all filters
    this.headerFilters.forEach(filter => {
      filter.active = false;
      if (filter.dropDownOptions) {
        filter.dropDownOptions.forEach(opt => opt.active = false);
      }
      filter.dateRangeFromControl?.reset();
      filter.dateRangeToControl?.reset();
      filter.customDateRange = false;
      filter.selectedDateRangeKey = '';
      filter.selectedDateRangeText = '';
    });

    // Clear sorting state
    if (this.columnsSubject) {
      const columns = this.columnsSubject.value;
      if (columns) {
        columns.forEach(col => {
          col.sortedAsc = false;
          col.sortedDesc = false;
        });
        this.columnsSubject.next([...columns]);
      }
    }

    // Clear search
    this.quickSearchControl.reset();
    this.quickSearchActive = false;
    this.quickSearchInProgress = false;

    this.handleFilterChange();
  }

  toggleButtonFilter(filter: HeaderFilterComponent) {
    filter.active = !filter.active;
    if (filter.active) {
      this.isAllFilterActive = false;
      if (filter.excludes) {
        this.headerFilters.forEach(headerFilter => {
          if (filter.excludes?.includes(headerFilter.parameterName)) {
            headerFilter.active = false;
          }
        });
      }
    }
    this.handleFilterChange();

  }


  handleFilterChange(skipSubjectPush: boolean = false): void {
    // 1) Recompute the “All” button
    const anyActive = this.headerFilters.some(f => {
      if (f.dateRange) {
        return !!(f.dateRangeFromControl?.value || f.dateRangeToControl?.value);
      }
      if (f.dropDownOptions) {
        return f.dropDownOptions.some(o => o.active);
      }
      return f.active;
    });
    this.isAllFilterActive = !anyActive;

    // Check if we need to enter edit mode
    this.checkForEditMode();

    if (this.fetchFiltersFromUrl) {
      const newParams: Record<string, any> = {
        ...this.activatedRoute.snapshot.queryParams
      };

      this.headerFilters.forEach(filter => {
        if (
          filter.dateRange &&
          filter.dateRangeFromParamKey &&
          filter.dateRangeToParamKey
        ) {
          const fromRaw = filter.dateRangeFromControl?.value;
          const toRaw   = filter.dateRangeToControl?.value;
          const hasFrom = fromRaw != null && fromRaw !== '';
          const hasTo   = toRaw   != null && toRaw   !== '';

          // Write only real ISO dates
          if (hasFrom) {
            newParams[filter.dateRangeFromParamKey] = new Date(fromRaw).toISOString();
          } else {
            delete newParams[filter.dateRangeFromParamKey];
          }

          if (hasTo) {
            newParams[filter.dateRangeToParamKey] = new Date(toRaw).toISOString();
          } else {
            delete newParams[filter.dateRangeToParamKey];
          }

          filter.active = hasFrom || hasTo;
        }
        else if (filter.dropDownOptions) {
          const vals = filter.dropDownOptions
            .filter(o => o.active)
            .map(o => o.value)
            .join(',');
          if (vals) {
            newParams[filter.parameterName] = vals;
            filter.active = true;
          } else {
            delete newParams[filter.parameterName];
            filter.active = false;
          }
        }
        else {
          if (filter.active) {
            newParams[filter.parameterName] = true;
          } else {
            delete newParams[filter.parameterName];
          }
        }

        if (filter.excludes && filter.active) {
          filter.excludes.forEach(ex => {
            delete newParams[ex];
            const exF = this.headerFilters.find(f2 => f2.parameterName === ex);
            if (exF) {
              exF.active = false;
              exF.dropDownOptions?.forEach(o => o.active = false);
              exF.dateRangeFromControl?.reset();
              exF.dateRangeToControl?.reset();
            }
          });
        }
      });

      // Add sorting parameters to URL
      this.updateSortingInUrl(newParams);

      if (this.paginationSubject) {
        newParams['page'] = 1;
        const pg = this.paginationSubject.value;
        if (pg.page !== 1) {
          pg.page = 1;
          this.paginationSubject.next(pg);
        }
      }
      this.router.navigate([], {
        relativeTo: this.activatedRoute,
        queryParams: newParams
      });
    }
    if (!skipSubjectPush) {
      this.headerFiltersContainerSubject.next({
        filters: this.headerFilters,
        init:    false
      });
    }
  }






  onDropDownOptionSelected(filterComponent: HeaderFilterComponent, option: HeaderFilterDropDownOption, event: Event) {
    if (!filterComponent.multiSelect) {
      filterComponent.dropDownOptions?.forEach((option) => {
        option.active = false;
      });
    }
    option.active = !option.active;
    let filterActive = false;
    filterComponent.dropDownOptions?.forEach((option) => {
      if (option.active) {
        filterActive = true;
      }
    });
    filterComponent.active = filterActive;

    this.handleFilterChange();
    event.stopPropagation();
  }

  clearDateRangeFilter(filter: HeaderFilterComponent) {
    filter.dateRangeFromControl?.reset();
    filter.dateRangeToControl?.reset();
    filter.active = false;
    filter.customDateRange = false;
    filter.selectedDateRangeKey = '';
    filter.selectedDateRangeText = '';

    this.handleFilterChange();
  }

  clearDropDownFilter(filterComponent: HeaderFilterComponent) {
    filterComponent.dropDownOptions?.forEach((option) => {
      option.active = false;
    });
    filterComponent.active = false;
    this.handleFilterChange();
  }

  applyCustomDateRange(filter: HeaderFilterComponent, _event: Event) {
    this.isAllFilterActive = false;
    filter.customDateRange = true;
    filter.active = true;
    filter.selectedDateRangeKey = 'custom';

    const startDateFormatted = new Date(filter.dateRangeFromControl!.value).toLocaleDateString();
    const endDateFormatted = new Date(filter.dateRangeToControl!.value).toLocaleDateString();
    filter.selectedDateRangeText = `${startDateFormatted} ${this.translateService.instant('common.to')} ${endDateFormatted}`;
    this.handleFilterChange();
  }

  resetFilters(resetSearchControl: boolean = true, skipHandleFilterChange: boolean = false) {
    this.headerFilters.forEach(filter => {
      filter.active = false;
      if (filter.dropDownOptions) {
        filter.dropDownOptions.forEach(option => {
          option.active = false;
        });
      }
      if (filter.dateRangeFromControl) {
        filter.dateRangeFromControl.reset();
      }
      if (filter.dateRangeToControl) {
        filter.dateRangeToControl.reset();
      }
    });

    // Reset sorting state
    if (this.columnsSubject) {
      const columns = this.columnsSubject.value;
      if (columns) {
        columns.forEach(col => {
          col.sortedAsc = false;
          col.sortedDesc = false;
        });
        this.columnsSubject.next([...columns]);
      }
    }

    if (resetSearchControl) {
      this.quickSearchControl.reset();
      if (this.fetchFiltersFromUrl) {
        const currentParams = this.activatedRoute.snapshot.queryParams;
        const paramsToUpdate: { [key: string]: any } = {};

        if (currentParams['search']) {
          paramsToUpdate['search'] = null;
        }

        // Clear sorting parameters
        if (currentParams['sortBy'] || currentParams['sortOrder']) {
          paramsToUpdate['sortBy'] = null;
          paramsToUpdate['sortOrder'] = null;
        }

        if (Object.keys(paramsToUpdate).length > 0) {
          this.updateUrlWithoutNewHistoryEntry(paramsToUpdate);
        }
      }
    }

    if (!skipHandleFilterChange) {
      this.handleFilterChange();
    }
  }

  selectDateRange(range: string, filter: HeaderFilterComponent, event: Event): void {
    filter.selectedDateRangeKey = range;
    filter.active = true;
    let momentFrom = moment();
    let momentTo = moment();

    switch (range) {
      case 'next7Days':
        momentTo = momentTo.add(6, 'days');
        break;
      case 'next30Days':
        momentTo = momentTo.add(29, 'days');
        break;
      case 'today':
        break;
      case 'last7Days':
        momentFrom = momentFrom.subtract(6, 'days');
        break;
      case 'last30Days':
        momentFrom = momentFrom.subtract(29, 'days');
        break;
      case 'last90Days':
        momentFrom = momentFrom.subtract(89, 'days');
        break;
      case 'last120Days':
        momentFrom = momentFrom.subtract(119, 'days');
        break;
      case 'custom':
        filter.customDateRange = true;
        event.stopPropagation();
        return;
      default:
        break;
    }

    if (range !== 'custom') {
      filter.customDateRange = false;
      filter.dateRangeFromControl?.setValue(momentFrom!.format('YYYY-MM-DD'));
      filter.dateRangeToControl?.setValue(momentTo!.format('YYYY-MM-DD'));
    }

    filter.selectedDateRangeText = 'tablerino.dateRange.' + range;
    filter.selectedDateRangeKey = range;

    this.handleFilterChange();
  }

  recogniseDateRange(rangeFrom: string | undefined, rangeTo: string | undefined): string {
    if (!rangeFrom || !rangeTo) {
      return 'custom';
    }
    const today = moment().format('YYYY-MM-DD');
    const last7DaysFrom = moment().subtract(6, 'days').format('YYYY-MM-DD');
    const last30DaysFrom = moment().subtract(29, 'days').format('YYYY-MM-DD');
    const last90DaysFrom = moment().subtract(89, 'days').format('YYYY-MM-DD');
    const last120DaysFrom = moment().subtract(119, 'days').format('YYYY-MM-DD');
    const next7DaysTo = moment().add(6, 'days').format('YYYY-MM-DD');
    const next30DaysTo = moment().add(29, 'days').format('YYYY-MM-DD');

    if (rangeFrom === today && rangeTo === today) {
      return 'today';
    }

    if (rangeFrom === last7DaysFrom && rangeTo === today) {
      return 'last7Days';
    }

    if (rangeFrom === last30DaysFrom && rangeTo === today) {
      return 'last30Days';
    }

    if (rangeFrom === last90DaysFrom && rangeTo === today) {
      return 'last90Days';
    }

    if (rangeFrom === last120DaysFrom && rangeTo === today) {
      return 'last120Days';
    }

    if (rangeFrom === today && rangeTo === next7DaysTo) {
      return 'next7Days';
    }

    if (rangeFrom === today && rangeTo === next30DaysTo) {
      return 'next30Days';
    }

    return 'custom';
  }

  onEnterPressed(_event: any): void {
    // When Enter is pressed, immediately trigger the search without waiting for debounce
    const searchTerm = this.quickSearchControl.value;
    if (searchTerm) {
      this.quickSearchInProgress = true;

      // Reset pagination to page 1 when search is performed with Enter
      if (this.fetchFiltersFromUrl && this.paginationSubject) {
        const paginationContainer = this.paginationSubject.value;

        // Reset page to 1
        if (paginationContainer.page !== 1) {
          paginationContainer.page = 1;
          this.paginationSubject.next(paginationContainer);

          // Update URL with search term and reset page
          this.updateUrlWithNavigate({
            search: searchTerm,
            page: 1
          });
        } else {
          // Just update the search term in the URL
          this.updateUrlWithNavigate({
            search: searchTerm
          });
        }
      }

      // Emit search event
      this.quickSearchEmitter.emit(searchTerm);
    }
  }

  resetColumns() {
    this.sharedService.saveUserTableColumns(this.tableName, []);
    setTimeout(() => {
      window.location.reload();
    }, 300);
  }

  toggleColumnVisibility(column: TablerinoColumn) {
    let columns = this.columnsSubject.value;
    columns.find((c) => c.name === column.name)!.visible = !column.visible;
    this.columnsSubject.next(columns);
    this.sharedService.saveUserTableColumns(this.tableName, columns);

    // Check for edit mode when column visibility changes
    this.checkForEditMode();
  }


  loadSavedViews(): Promise<void> {
    if (!this.tableName) return Promise.resolve();
    return new Promise((resolve) => {
      this.sharedService.getTableViews({}).subscribe(
        (views) => {
          this.savedViews = views.filter(view => view.table_name === this.tableName);
          resolve();
        },
        (error) => {
          console.error('Error loading saved views:', error);
          resolve();
        }
      );
    });
  }

  openSaveViewDialog() {
    const modalRef = this.modalService.open(SaveViewModalComponent, { size: 'md' });
    modalRef.componentInstance.editingViewId = this.editingViewId;
    modalRef.componentInstance.viewName = this.newViewName;

    modalRef.result.then((result) => {
      if (result) {
        this.newViewName = result.name;
        if (result.id) {
          this.updateView(result.id);
        } else {
          this.saveCurrentView();
        }
      }
    }, (reason) => {
    });
  }

  saveCurrentView() {
    if (!this.tableName) {
      this.toastService.successToast('Table name is required');
      return;
    }
    const filterState = this.collectCurrentFilterState();
    // Store the view name before clearing it
    const savedViewName = this.newViewName;

    this.sharedService.saveTableView({
      table_name: this.tableName,
      table_config: {
        name: this.newViewName,
        ...filterState
      }
    }).subscribe(
      (response) => {
        this.showSaveViewDialog = false;
        this.newViewName = '';
        this.creatingNewView = false;
        this.filtersModified = false;
        this.loadSavedViews().then(() => {
          // Use the stored view name to find the newly created view
          const newView = this.savedViews.find(view => view.table_config.name === savedViewName);
          if (newView) {
            this.activeViewId = newView.entry_id;
            this.applyView(newView);
          }
        });

        this.toastService.successToast('saved');
      },
      error => {
        this.toastService.successToast('Failed to save view');
      }
    );
  }

  updateView(viewId: number) {
    const filterState = this.collectCurrentFilterState();

    this.sharedService.updateTableView({
      entry_id: viewId,
      table_config: {
        name: this.newViewName,
        ...filterState
      }
    }).subscribe(
      (response) => {
        this.showSaveViewDialog = false;
        this.newViewName = '';
        this.creatingNewView = false;

        this.loadSavedViews().then(() => {
          this.activeViewId = viewId;
          const updatedView = this.savedViews.find(view => view.entry_id === viewId);
          if (updatedView) {
            this.applyView(updatedView);
          }
        });

        this.toastService.successToast('common.create');
      },
      error => {
        console.log("Failed to delete", error)
      }
    );
  }

  deleteView(entryId: number, event?: Event) {
    if (event) {
      event.stopPropagation();
    }

    if (this.activeViewId === entryId) {
      this.activeViewId = null;
    }

    const modalRef = this.modalService.open(VerifyPopupModal, { size: 'md' });
    modalRef.componentInstance.title = this.translateService.instant('common.confirmDelete');
    modalRef.componentInstance.message = this.translateService.instant('tablerino.confirmDeleteView');
    modalRef.componentInstance.confirmButtonText = this.translateService.instant('common.delete');
    modalRef.componentInstance.cancelButtonText = this.translateService.instant('common.cancel');

    modalRef.result.then((result) => {
      if (result) {
        this.sharedService.deleteTableView(entryId).subscribe(
          () => {
            this.toastService.successToast(this.translateService.instant('common.deleted'));
            this.loadSavedViews();
          },
          error => {
            console.log("Failed to delete", error)
          }
        );
      }
    }, () => {
    });
  }

  applyView(view: any): void {
    this.isApplyingView = true; // Set flag to prevent premature view deselection
    this.activeViewId = view.entry_id;

    const filters = view.table_config.filters as SavedFilter[] | undefined;
    const search  = view.table_config.search as string | null;
    const sorting = view.table_config.sorting as SavedSortingState | undefined;

    this.resetFilters(true, true); // Skip handleFilterChange to prevent premature change detection

    if (filters) {
      filters.forEach((savedFilter: SavedFilter) => {
        const filter = this.headerFilters.find(f => f.parameterName === savedFilter.parameterName);
        if (!filter) return;

        if (!filter.dateRange) {
          filter.active = savedFilter.active;
        }

        if (savedFilter.dropDownOptions && filter.dropDownOptions) {
          savedFilter.dropDownOptions.forEach((savedOption: SavedFilterDropDownOption) => {
            const opt = filter.dropDownOptions!.find(o => o.value === savedOption.value);
            if (opt) {
              opt.active = savedOption.active;
            }
          });
          filter.active = filter.dropDownOptions.some(o => o.active);
        }

        if (
          savedFilter.dateRange &&
          filter.dateRangeFromControl &&
          filter.dateRangeToControl) {
          const from = savedFilter.dateRange.from;
          const to   = savedFilter.dateRange.to;

          const toYmd = (d: string | Date) =>
            new Date(d).toISOString().substring(0, 10);

          if (from) {
            filter.dateRangeFromControl.setValue(toYmd(from));
          } else {
            filter.dateRangeFromControl.reset();
          }
          if (to) {
            filter.dateRangeToControl.setValue(toYmd(to));
          } else {
            filter.dateRangeToControl.reset();
          }

          filter.selectedDateRangeKey = savedFilter.dateRange.selectedDateRangeKey;
          filter.customDateRange      = savedFilter.dateRange.customDateRange;

          filter.active = !!(from || to);
        }
      });
    }

    if (search) {
      this.quickSearchControl.setValue(search);
      this.quickSearchActive = true;
    }

    // Restore sorting state
    if (sorting && this.columnsSubject) {
      const columns = this.columnsSubject.value;
      if (columns) {
        columns.forEach(col => {
          col.sortedAsc = false;
          col.sortedDesc = false;
        });

        // Apply the saved sorting
        const targetColumn = columns.find(col => col.name === sorting.columnName);
        if (targetColumn) {
          if (sorting.direction === 'asc') {
            targetColumn.sortedAsc = true;
          } else {
            targetColumn.sortedDesc = true;
          }
        }

        // Update the columns subject to trigger UI updates
        this.columnsSubject.next([...columns]);
      }
    }

    this.originalViewState = this.collectCurrentFilterState();
    this.isApplyingView = false; // Clear flag after view application is complete
    this.handleFilterChange();
  }


  private collectCurrentFilterState(): SavedFilterState {
    // Collect sorting state from columns
    const sortedColumn = this.columnsSubject?.value?.find(col => col.sortedAsc || col.sortedDesc);
    const sortingState: SavedSortingState | undefined = sortedColumn ? {
      columnName: sortedColumn.name,
      direction: sortedColumn.sortedAsc ? 'asc' : 'desc'
    } : undefined;

    return {
      filters: this.headerFilters.map(filter => ({
        parameterName: filter.parameterName,
        translationKey: filter.translationKey,
        active: filter.active,
        dropDownOptions: filter.dropDownOptions?.map(opt => ({
          value: opt.value,
          translationKey: opt.translationKey,
          active: opt.active
        })),
        dateRange: filter.dateRangeFromControl && filter.dateRangeToControl ? {
          from: filter.dateRangeFromControl.value,
          to: filter.dateRangeToControl.value,
          selectedDateRangeKey: filter.selectedDateRangeKey,
          customDateRange: filter.customDateRange
        } : undefined,
        multiSelect: filter.multiSelect,
        excludes: filter.excludes
      })),
      search: this.quickSearchControl.value,
      sorting: sortingState
    };
  }

  startCreatingNewView() {
    this.creatingNewView = true;
    this.activeViewId = null;
    this.resetFilters();
  }

  cancelCreatingNewView() {
    this.creatingNewView = false;
    if (this.activeViewId) {
      const activeView = this.savedViews.find(view => view.entry_id === this.activeViewId);
      if (activeView) {
        this.applyView(activeView);
      }
    }
  }

  checkForEditMode(): void {
    if (!this.activeViewId || this.creatingNewView) {
      return;
    }

    if (this.originalViewState) {
      const currentState = this.collectCurrentFilterState();
      if (this.hasStateChanged(this.originalViewState, currentState)) {
        // Instead of entering edit mode, deselect the current view
        this.deselectCurrentView();
      }
    }
  }

  hasStateChanged(originalState: SavedFilterState, currentState: SavedFilterState): boolean {
    if (originalState.search !== currentState.search) {
      return true;
    }

    // Check if sorting state has changed
    if (originalState.sorting !== currentState.sorting) {
      if (!originalState.sorting && currentState.sorting) {
        return true;
      }
      if (originalState.sorting && !currentState.sorting) {
        return true;
      }
      if (originalState.sorting && currentState.sorting) {
        if (originalState.sorting.columnName !== currentState.sorting.columnName ||
            originalState.sorting.direction !== currentState.sorting.direction) {
          return true;
        }
      }
    }

    if (originalState.filters.length !== currentState.filters.length) {
      return true;
    }

    for (let i = 0; i < originalState.filters.length; i++) {
      const originalFilter = originalState.filters[i];
      const currentFilter = currentState.filters[i];

      if (originalFilter.parameterName !== currentFilter.parameterName ||
        originalFilter.active !== currentFilter.active) {
        return true;
      }

      if (originalFilter.dropDownOptions && currentFilter.dropDownOptions) {
        if (originalFilter.dropDownOptions.length !== currentFilter.dropDownOptions.length) {
          return true;
        }
        for (let j = 0; j < originalFilter.dropDownOptions.length; j++) {
          if (originalFilter.dropDownOptions[j].active !== currentFilter.dropDownOptions[j].active) {
            return true;
          }
        }
      } else if (originalFilter.dropDownOptions !== currentFilter.dropDownOptions) {
        return true;
      }

      if (originalFilter.dateRange && currentFilter.dateRange) {
        if (originalFilter.dateRange.from !== currentFilter.dateRange.from ||
          originalFilter.dateRange.to !== currentFilter.dateRange.to) {
          return true;
        }
      } else if (originalFilter.dateRange !== currentFilter.dateRange) {
        return true;
      }
    }

    return false;
  }

  deselectCurrentView(): void {
    this.activeViewId = null;
    this.originalViewState = null;
  }

  getActiveViewName(): string {
    if (!this.activeViewId) {
      return '';
    }
    const activeView = this.savedViews.find(view => view.entry_id === this.activeViewId);
    return activeView ? activeView.table_config.name : '';
  }


}
