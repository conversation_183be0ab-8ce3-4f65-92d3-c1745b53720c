.table-scroll-container {
  width: 100%;
  overflow-x: auto;
  white-space: nowrap;
  border-right: 1px solid #EBEEF1;
  border-left: 1px solid #EBEEF1;
  background-color: white;
}

.table-cell.header-cell:hover {
  background-color: #f5f5f5 !important;
  cursor: pointer;
}

/* When disableDrag is true, remove hover effect/cursor */
.table-cell.header-cell.no-drag:hover {
  background-color: #fcfcfc !important;
  cursor: default !important;
}

.table-responsive {
  display: table;
  width: auto;
  border-collapse: separate;
  border-spacing: 0;
}

.table-header,
.table-row {
  display: table-row;
}

.table-row:hover {
  background-color: #f5f5f5 !important;
  cursor: pointer;
}

.table-cell {
  display: table-cell;
  padding: 8px 10px;
  border-top: 1px solid #EBEEF1;
  box-sizing: border-box;
  text-align: left;
  white-space: nowrap;
  overflow: visible;
}

.table-header .table-cell {
  font-weight: bold;
  color: #333;
  padding: 8px 10px;
  font-size: 13px;
  background-color: #fcfcfc;
  transition: background-color 0.3s ease;
  cursor: pointer;
}

.template-cell {
  padding: 0 0 !important;
}


.table-header .table-cell:hover {
  background-color: #f5f5f5;

}

.table-row:last-child .table-cell {
  border-bottom: 1px solid #EBEEF1;
}

.table-row:hover {
  background-color: #f5f5f5;
  cursor: pointer;
}

.table-header:hover {
  background-color: inherit;
  cursor: default;
}

.spinner-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
}

.spinner {
  border: 4px solid #f3f3f3;
  border-top: 4px solid #3498db;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 2s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.table-cell.resizable {
  position: relative;
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
  padding-right: 20px;
}

.resizer {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 5px;
  cursor: col-resize;
  user-select: none;
  background-color: transparent;
}

.resizer:hover {
  background-color: #ccc;
}

.cdk-drag-preview {
  box-sizing: border-box;
  border-radius: 4px;
  box-shadow: 0 5px 5px -3px rgba(0, 0, 0, 0.2),
  0 8px 10px 1px rgba(0, 0, 0, 0.14),
  0 3px 14px 2px rgba(0, 0, 0, 0.12);
}

.cdk-drag-placeholder {
  opacity: 0;
}

.cdk-drag-animating {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.table-responsive.cdk-drop-list-dragging .table-cell:not(.cdk-drag-placeholder) {
  transition: transform 250ms cubic-bezier(0, 0, 0.2, 1);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.column-toggle {
  margin-left: 8px;
}


.column-toggle input[type="checkbox"] {
  cursor: pointer;
  width: 16px;
  height: 16px;
}

.all-columns-list {
  margin-top: 10px;
}

.column-item {
  cursor: pointer;
  margin-bottom: 5px;
}

.hidden-column {
  color: blue;
  text-decoration: underline;
}

.hidden-column:hover {
  color: darkblue;
}

.header-cell {
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
}

.header-cell.last-visible {
  width: 100%;
}

.header-content {
  display: flex;
  align-items: center;
  width: 100%;
}

.column-icon {
  display: none;
  margin-left: 8px;
  width: 10px;
}

.column-icon-placeholder {
  width: 10px;
  margin-left: 8px;
}

.header-cell:hover .column-icon-placeholder {
  display: none;
}

.header-cell:hover .column-icon {
  display: inline-block;
}


.customer-chevron {
  margin-left: 8px;
}

.cell-content {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.customer-tooltip {
  position: fixed;
  padding: 10px;
  min-height: 100px;
  min-width: 200px;
  background-color: white;
  justify-content: center;
  font-size: 16px;
  border-radius: 10px;
  border: 1px solid #e5e8ec;
  z-index: 1000;
  pointer-events: auto;
  box-shadow: #e5e5e5 0 5px 8px ;
}

.debug-square button {
/*  margin-top: 10px;*/
/*  padding: 8px 12px;*/
/*  background-color: #d6dbe1;*/
/*  color: white;*/
/*  border: none;*/
  border-radius: 10px;
/*  cursor: pointer;*/
}

.table-loader{
  width: 100%;
  border: 1px solid #ebeef1;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #FFFFFF;
}

.table-font-size {
  font-size: 14px;
}

.table-cell.customer-column-hover {
  transition: background-color 0.3s ease;
}

.table-cell.customer-column-hover:hover {
  background-color: #ececec;
}

.no-data-row {
  display: table-cell;
  width: 100%;
  text-center: center;
  padding: 8px;
  border-top: 1px solid #EBEEF1;
  border-bottom: 1px solid #EBEEF1;
  box-sizing: border-box;
}

orders-table-container{
  max-width:2000px;
}

/* Table row link styles */
.table-row-link {
  display: contents;
  color: inherit;
}

.table-row-link:hover {
  color: inherit;
  text-decoration: none;
}

.table-row-link:visited {
  color: inherit;
}

.table-row-link:focus {
  color: inherit;
  text-decoration: none;
}

.table-row-content {
  display: contents;
}


/* --------------------------------------------- */
/* Responsive Card Layout for Small Screens      */
/* --------------------------------------------- */



@media (max-width: 767px) {
  .table-scroll-container {
    overflow-x: visible;
    white-space: normal;
  }

  .table-row:nth-child(even) {
    background-color: #f7f7f7;
  }


  .table-row:hover {
    background-color: #e3e3e3 !important;
  }

  .table-responsive {
    display: block;
    width: 100%;
  }

  .table-row {
    display: block !important;
    border: 1px solid #ddd;
    background-color: #fff;
  }

  .table-cell {
    display: flex !important;
    align-items: center;
    justify-content: space-between;
    width: 100% !important;
    border: none !important;
    /*border-bottom: 1px solid #eee !important;*/
    white-space: normal !important;
    overflow: visible !important;
    position: relative;
  }

  .table-row:last-child .table-cell {
    border-bottom: none !important;
  }


  .table-cell::before {
    content: attr(data-label) ": ";
    font-weight: bold;
    margin-right: 5px;
    color: #333;
    white-space: nowrap;
  }
}

.initials-box {
  width: 25px;
  height: 25px;
  background-color: #f0f0f0;
  color: #80888E;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: bold;
  font-size: 15px;
  border-radius: 5px;
  text-overflow: ellipsis;
  overflow: hidden; /* Ensures the text does not overflow the box */
  white-space: nowrap; /* Prevents the text from wrapping */
}
