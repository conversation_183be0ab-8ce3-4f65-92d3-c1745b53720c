<div class="table-scroll-container">
    <div *ngIf="!isMobile" class="">
      <!-- Table Header -->
      <div id="tableHeader" class="table-row table-header" cdkDropList cdkDropListOrientation="horizontal" cdkDropListLockAxis="x" (cdkDropListDropped)="drop($event)" [cdkDropListDisabled]="isResizing || disableDrag" >
        <div *ngIf="settings.checkboxes" class="table-cell header-cell" [style.display]="'table-cell'" (click)="$event.stopPropagation();">
          <input class="form-check-input" type="checkbox" [checked]="allSelected" (change)="selectAllRows($event)"/>
        </div>

        <div *ngFor="let column of visibleColumns; let i = index"
             [style.display]="column.visible ? 'table-cell' : 'none'"
             [style.width.px]="columnWidths[columns.indexOf(column)]"
             [attr.data-label]="column.hideHeaderName ? column.name : (column.labelKey | translate)"
             cdkDrag
             [cdkDragDisabled]="isResizing || disableDrag || column.buttonColumn"
             [ngClass]="{'no-drag': disableDrag || column.buttonColumn}"
             class="table-cell header-cell">
          <div class="header-content">
            <div class="d-flex col align-items-center" (click)="sortColumn(column)">
              {{ column.hideHeaderName ? '' : (column.labelKey | translate) }}
              <i *ngIf="column.sort && !column.sortedAsc && !column.sortedDesc && !disableSort" class="fa-light fa-sort ms-1"></i>
              <i *ngIf="column.sortedAsc && !disableSort" class="fa-light fa-sort-up ms-1"></i>
              <i *ngIf="column.sortedDesc && !disableSort" class="fa-light fa-sort-down l ms-1"></i>
            </div>
            <i cdkDragHandle *ngIf="!disableDrag && !column.buttonColumn" class="fa-regular fa-grip-dots-vertical column-icon fa-xl"></i>
            <i *ngIf="!disableDrag && !column.buttonColumn" class="column-icon-placeholder"></i>
          </div>
        </div>
      </div>
      <ng-container *ngIf="!loading">
      <!-- Table Body -->
        <ng-container *ngIf="tableData && tableData.length > 0; else noDataTemplate">
          <div *ngFor="let row of tableData;" class="table-row">
            <!-- Row with router link -->
            <a *ngIf="hasRouterLink() && generateRouterLink(row)"
               [routerLink]="generateRouterLink(row)"
               class="table-row-link d-contents text-decoration-none"
               (click)="onRowClick(row)">
              <div *ngIf="settings.checkboxes" class="table-cell" container="body" [ngbTooltip]="row.disableSelectTooltip" (click)="$event.stopPropagation(); $event.preventDefault();">
                <input class="form-check-input" [disabled]="row.disableSelect" type="checkbox" [(ngModel)]="row.selected" (change)="onRowSelect()"/>
              </div>
              <div
                *ngFor="let column of visibleColumns; let i = index"
                [style.width.px]="columnWidths[columns.indexOf(column)]"
                [attr.data-label]="column.labelKey | translate"
                class="table-cell"
                [style.vertical-align]="column.align || ''"
                [ngClass]="{'first-visible': i === 0,'last-visible': i === visibleColumns.length - 1, 'template-cell': !!column.ngTemplate}">
                <ng-container *ngTemplateOutlet="column.ngTemplate || defaultCell; context: { column: column, row: row }"></ng-container>
              </div>
            </a>

            <!-- Row without router link (fallback to click handler) -->
            <ng-container *ngIf="!hasRouterLink() || !generateRouterLink(row)">
              <div class="table-row-content" (click)="onRowClick(row)">
                <div *ngIf="settings.checkboxes" class="table-cell" container="body" [ngbTooltip]="row.disableSelectTooltip" (click)="$event.stopPropagation();">
                  <input class="form-check-input" [disabled]="row.disableSelect" type="checkbox" [(ngModel)]="row.selected" (change)="onRowSelect()"/>
                </div>
                <div
                  *ngFor="let column of visibleColumns; let i = index"
                  [style.width.px]="columnWidths[columns.indexOf(column)]"
                  [attr.data-label]="column.labelKey | translate"
                  class="table-cell"
                  [style.vertical-align]="column.align || ''"
                  [ngClass]="{'first-visible': i === 0,'last-visible': i === visibleColumns.length - 1, 'template-cell': !!column.ngTemplate}">
                  <ng-container *ngTemplateOutlet="column.ngTemplate || defaultCell; context: { column: column, row: row }"></ng-container>
                </div>
              </div>
            </ng-container>
          </div>
        </ng-container>
      </ng-container>
    </div>

      <!-- Loading Spinner -->
      <div *ngIf="loading" class="table-loader">
        <div class="spinner-border my-3">
          <span class="visually-hidden">Loading...</span>
          <span class="spinner-border-sm"></span>
        </div>
      </div>

    <div *ngIf="isMobile" class="table-responsive">
      <div *ngFor="let row of tableData;" class="table-row">
        <!-- Mobile row with router link -->
        <a *ngIf="hasRouterLink() && generateRouterLink(row)"
           [routerLink]="generateRouterLink(row)"
           class="table-row-link d-contents text-decoration-none"
           (click)="onRowClick(row)">
          <div *ngIf="settings.checkboxes" class="table-cell" (click)="$event.stopPropagation(); $event.preventDefault();">
            <input class="form-check-input" [disabled]="row.disableSelect" type="checkbox" [(ngModel)]="row.selected" (change)="onRowSelect()"/>
          </div>
          <div *ngFor="let column of visibleColumns; let i = index" class="table-cell" [attr.data-label]="column.labelKey | translate">
            <ng-container *ngTemplateOutlet="column.ngTemplate || defaultCell; context: { column: column, row: row }"></ng-container>
          </div>
        </a>

        <!-- Mobile row without router link -->
        <ng-container *ngIf="!hasRouterLink() || !generateRouterLink(row)">
          <div class="table-row-content" (click)="onRowClick(row)">
            <div *ngIf="settings.checkboxes" class="table-cell" (click)="$event.stopPropagation();">
              <input class="form-check-input" [disabled]="row.disableSelect" type="checkbox" [(ngModel)]="row.selected" (change)="onRowSelect()"/>
            </div>
            <div *ngFor="let column of visibleColumns; let i = index" class="table-cell" [attr.data-label]="column.labelKey | translate">
              <ng-container *ngTemplateOutlet="column.ngTemplate || defaultCell; context: { column: column, row: row }"></ng-container>
            </div>
          </div>
        </ng-container>
      </div>
    </div>
</div>

<ng-template #defaultCell let-column="column" let-row="row">
  <div *ngIf="!column.cellTemplateName" [innerHTML]="callFormatter(column, row)"></div>
  <ng-container *ngIf="column.cellTemplateName === 'favourite';">
    <ng-container *ngTemplateOutlet="favouriteCell; context: { column: column, row: row }"></ng-container>
  </ng-container>
  <ng-container *ngIf="column.cellTemplateName === 'toggleSwitch';">
    <ng-container *ngTemplateOutlet="toggleSwitchCell; context: { column: column, row: row }"></ng-container>
  </ng-container>
</ng-template>

<ng-template #favouriteCell let-column="column" let-row="row">
  <div [id]="column.name">
    <div [innerHTML]="favouriteStar(row.favourite)" (click)="favouriteToggled($event, row)" style="width: 23px;"></div>
  </div>
</ng-template>

<ng-template #toggleSwitchCell let-column="column" let-row="row">
  <div class="cursor-pointer">
    <app-toggle-switch [id]="column.name" [state]="row[column.cellTemplateSourceKey]" (stateChange)="toggleSwitchChanged($event, row, column)"></app-toggle-switch>
  </div>
</ng-template>

<!-- No Data Template -->
<ng-template #noDataTemplate>
  <div class="table-row d-flex justify-content-center py-4 border-bottom border-top">
    {{ noResultsTranslationKey | translate }}
  </div>
</ng-template>
