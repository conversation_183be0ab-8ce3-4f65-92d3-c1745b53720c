import {Component, EventEmitter, HostListener, Input, OnInit, Output, TemplateRef,} from '@angular/core';
import {CommonModule} from "@angular/common";
import {CdkDrag, CdkDragDrop, CdkDragHandle, CdkDropList, DragDropModule, moveItemInArray} from "@angular/cdk/drag-drop";
import {RouterModule} from "@angular/router";
import {OrderListService} from "../../services/order-list.service";

import {TranslateModule} from "@ngx-translate/core";
import {favouriteStar, UtilsService} from "../../../@core/utils/utils.service";
import {FormsModule} from "@angular/forms";
import {environment} from "../../../../environments/environment";
import {BehaviorSubject} from "rxjs";

import {SharedService} from "../../services/shared.service";
import {StandardImports} from "../../global_import";
import {ToggleSwitchComponent} from "../toggle-switch/toggle-switch.component";


export interface TablerinoSettings {
  clickableRows?: boolean;
  checkboxes?: boolean
  hideHeaders?: boolean;
  routerLinkKey?: string; // Key in row data to use for router link generation
  routerLinkPrefix?: string; // Prefix to add before the key value (e.g., '/products/')
}

export interface TableRow {
  selected?: boolean;
  favourite?: boolean;
  disableSelect?: boolean;
  disableSelectTooltip?: string | null;
}

export interface TablerinoColumn {
  // Do not name your column "status", it will break the table and I don't know why
  name: string;
  label?: string;
  labelKey: string;
  formatter: (a: any) => any | string;
  sort?: boolean;
  sortedAsc?: boolean;
  sortedDesc?: boolean;
  justify?: string;
  align?: string;
  width?: number;
  visible: boolean;
  hideHeaderName?: boolean;
  cellTemplateName?: 'favourite' | 'users' | 'toggleSwitch';
  cellTemplateSourceKey?: string;
  ngTemplate?: TemplateRef<any>;
  buttonColumn?: boolean;
}

@Component({
  selector: 'app-tablerino',
  templateUrl: './tablerino.component.html',
  styleUrls: ['./tablerino.component.css'],
  standalone: true,
  imports: [StandardImports, CdkDrag, CdkDragHandle, CdkDropList, ToggleSwitchComponent, RouterModule]
})

export class TablerinoComponent implements OnInit {
  @Input() tableName: string;
  @Input() tableData: any[] = [];
  @Input() columnsSubject: BehaviorSubject<TablerinoColumn[]>;
  @Input() settings: TablerinoSettings = {};
  @Input() loading: boolean = false;
  @Input() selectedRowsSubject: BehaviorSubject<any>;
  @Input() noResultsTranslationKey: string = 'common.noResults';
  @Input() disableDrag: boolean = false;
  @Input() disableSort: boolean = false;

  columns: TablerinoColumn[] = [];
  columnWidths: Array<number | null> = [];
  totalTableWidth: number | null = 0;
  isResizing: boolean = false;
  allSelected: boolean = false;
  paginationDetails = {page: 1, limit: environment.standardPageSize}
  visibleColumns: TablerinoColumn[] = [];
  public isMobile: boolean = false;

  @Output() rowClickedEmitter: EventEmitter<any> = new EventEmitter();
  @Output() selectedRowsChangedEmitter: EventEmitter<any> = new EventEmitter();
  @Output() favouriteToggledEmitter: EventEmitter<any> = new EventEmitter();
  @Output() toggleEmitter: EventEmitter<any> = new EventEmitter();
  @Output() sortEmitter: EventEmitter<TablerinoColumn> = new EventEmitter();

  constructor(
    public utilsService: UtilsService,
    private sharedService: SharedService
  ) {}

  ngOnInit() {
    this.isMobile = window.innerWidth <= 767;
    this.columnsSubject.subscribe(columns => {
      this.columns = columns;
      this.updateVisibleColumns();
      this.initializeColumnWidths();
    });
  }


  @HostListener('window:resize', ['$event'])
  onResize() {
    this.isMobile = window.innerWidth <= 767;
  }


  updateVisibleColumns() {
    // Filter the columns for only those visible
    this.visibleColumns = this.columns.filter(col => col.visible);
    this.calculateTotalWidth();
  }


  initializeColumnWidths() {
    this.columns.forEach((column, index) => {
      this.columnWidths[index] = column.width || 1200;
    });
    this.calculateTotalWidth();
  }


  adjustColumnWidths() {
    const visibleColumns = this.columns.filter(col => col.visible);

    let totalVisibleWidth = 0;
    visibleColumns.forEach((col, index) => {
      totalVisibleWidth += this.columnWidths[this.columns.indexOf(col)] ? this.columnWidths[this.columns.indexOf(col)]! : 0;
    });

    visibleColumns.forEach((col, index) => {
      const columnIndex = this.columns.indexOf(col);
      this.columnWidths[columnIndex] = (this.columnWidths[columnIndex]! / totalVisibleWidth) * this.totalTableWidth!;
    });
  }


  calculateTotalWidth() {
    this.totalTableWidth = this.columnWidths.reduce((sum, width) => sum! + width!, 0);
    this.adjustColumnWidths();
  }


  callFormatter(column: TablerinoColumn, data: any): any {
    return column.formatter(data);
  }


  drop(event: CdkDragDrop<string[]>) {
    if (!this.isResizing) {
      moveItemInArray(this.visibleColumns, event.previousIndex, event.currentIndex);
      const widthMap = new Map<string, number | null>();
      this.columns.forEach((col, i) => widthMap.set(col.name, this.columnWidths[i]));
      const hiddenColumns = this.columns.filter(col => !col.visible);
      this.columns = [...this.visibleColumns, ...hiddenColumns];
      this.columnWidths = this.columns.map(col => widthMap.get(col.name) ?? 400);
      this.sharedService.saveUserTableColumns(this.tableName, this.columns);
      this.columnsSubject.next(this.columns);
    }
  }


  selectAllRows(event: Event): void {
    const isChecked = (event.target as HTMLInputElement).checked;
    this.allSelected = isChecked;
    this.tableData.forEach(row => (row.selected = row.disableSelect ? false : isChecked));
    this.updateSelectedRows();
  }


  onRowSelect(): void {
    this.allSelected = this.tableData.every(row => row.selected);
    this.updateSelectedRows();
  }


  updateSelectedRows(): void {
    const selectedRows = this.tableData.filter(row => row.selected);
    this.selectedRowsChangedEmitter.emit(selectedRows)
    if (this.selectedRowsSubject) {
      this.selectedRowsSubject.next(selectedRows)
    }
  }


  favouriteToggled(event: Event, row: any) {
    event.stopPropagation();
    this.favouriteToggledEmitter.emit(row);
  }

  sortColumn(column: TablerinoColumn) {
    if (column.sort) {
      this.columns.forEach(col => {
        if (col.name !== column.name) {
          col.sortedAsc = false;
          col.sortedDesc = false;
        }
      });
      if (column.sortedAsc) {
        column.sortedAsc = false;
        column.sortedDesc = true;
      } else if (column.sortedDesc) {
        column.sortedAsc = false;
        column.sortedDesc = false;
      } else {
        column.sortedDesc = false;
        column.sortedAsc = true;
      }
      this.columnsSubject.next(this.columns);
      this.sortEmitter.emit(column);
    }
  }


  toggleSwitchChanged(event: Event, row: any, column: TablerinoColumn) {
    // event.stopPropagation();
    row[column.cellTemplateSourceKey!] = !row[column.cellTemplateSourceKey!];
    this.toggleEmitter.emit(row);
  }


  onRowClick(row: any) {
    this.rowClickedEmitter.emit(row)
  }

  generateRouterLink(row: any): string | null {
    if (this.settings.routerLinkKey && this.settings.routerLinkPrefix && row[this.settings.routerLinkKey]) {
      return `${this.settings.routerLinkPrefix}${row[this.settings.routerLinkKey]}`;
    }
    return null;
  }

  hasRouterLink(): boolean {
    return !!(this.settings.routerLinkKey && this.settings.routerLinkPrefix);
  }


  protected readonly favouriteStar = favouriteStar;
}

