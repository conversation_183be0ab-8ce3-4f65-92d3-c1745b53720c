/* Set the background color when the switch is off */
.big-switch:not(:checked) {
  background-color: #ececec;
}

.big-switch {
  border: none;
  box-shadow: none;
  /*transform: scale(1.5);*/
  /*margin: 10px;*/
  min-width: 44px; /* Set a minimum width to make the switch longer */
  min-height: 24px;
}

.form-check-input.big-check {
  transform: scale(1.5); /* Increase size by 1.5x */
  margin-right: 14px;
}

.switch {
  margin-top: 0;
  margin-right: 7px;
  border-color: #d8d9da;
}

.form-check-input {
  margin-top: 0;
  margin-right: 7px;
  border-color: #d8d9da;
}

/* Change color of checkbox  */
.switch:checked {
  background-color: #448C74 !important;
}
