<div class="form-check d-flex align-items-center" [ngClass]="divClasses" [ngbTooltip]="isDisabled && disabledTooltipKey ? (disabledTooltipKey | translate) : ''">
  <input *ngIf="control" type="checkbox" role="switch" class="form-check-input" id="switch_{{labelKey}}{{customId}}" [ngClass]="{'switch': !useCheckbox, 'big-switch': bigSwitch}" [formControl]="control"/>
  <input *ngIf="!control" [disabled]="isDisabled" role="switch" type="checkbox" class="form-check-input" [ngClass]="{'switch': !useCheckbox, 'big-check': bigCheck, 'big-switch': bigSwitch}" id="switch_{{labelKey}}{{customId}}" [(ngModel)]="state" (change)="internalToggle()"/>
  <label class="form-check-label" [style.padding-top.px]="bigSwitch ? 2 : 2" [ngClass]="labelClasses" for="switch_{{labelKey}}{{customId}}">{{ labelKey | translate }}</label>
</div>

