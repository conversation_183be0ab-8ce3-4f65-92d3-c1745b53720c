import {Component, Input, Output, OnInit, OnDestroy, EventEmitter, OnChanges} from '@angular/core';
import {FormControl, Validators} from '@angular/forms';
import { TranslateService } from '@ngx-translate/core';
import { Subscription } from 'rxjs';
import {StandardImports} from "../../global_import";

@Component({
    selector: 'app-toggle-switch',
    templateUrl: './toggle-switch.component.html',
    styleUrls: ['./toggle-switch.component.css'],
    standalone: true,
    imports: [StandardImports]
})
export class ToggleSwitchComponent implements OnInit, OnDestroy, OnChanges {
  @Input() labelKey: string = '';
  @Input() disabledTooltipKey: string;
  @Input() customSwitchClasses: string;
  @Input() customLabelClasses: string;
  @Input() labelTooltip: string;
  @Input() isDisabled: boolean = false;
  @Input() control: FormControl;
  @Input() bigSwitch: boolean = false;
  @Input() customId: string = '';
  @Input() bigCheck: boolean = false;
  @Input() useCheckbox: boolean = false;

  @Input() state: boolean;
  @Output() stateChange = new EventEmitter<any>();


  divClasses: string[] = []
  labelClasses: string[] = []

  private valueChangesSubscription: Subscription;

  constructor() {}

  ngOnInit() {
    if (!this.useCheckbox) {
      this.bigCheck = false;
      this.divClasses.push('form-switch');
    } else {
      this.bigSwitch = false;
    }

    if (this.control) {
      this.isDisabled ? this.control.disable() : this.control.enable()
      this.valueChangesSubscription = this.control.valueChanges.subscribe(value => {
        this.stateChange.emit(value);
      });
    }

    this.setLabelClasses();

  }

  ngOnChanges() {
    if (this.control) {
      this.isDisabled ? this.control.disable() : this.control.enable()
    }
  }

  internalToggle() {
    if (this.isDisabled) {
      return;
    }
    this.stateChange.emit(this.state)
  }

  setLabelClasses() {
  }

  ngOnDestroy(): void {
    if (this.valueChangesSubscription) {
      this.valueChangesSubscription.unsubscribe();
    }
  }
}

