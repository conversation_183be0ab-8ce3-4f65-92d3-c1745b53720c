.push {
  margin-left: auto;
}
.navbar-custom{
  background-color: #fafbfe!important;
}

.mobile-view {
  margin-left: 0 !important;
}

.button-menu-mobile {
  border: none;
  color: #313a46;
  height: 70px;
  line-height: 70px;
  width: 60px;
  background-color: transparent;
  font-size: 24px;
  cursor: pointer;
  /*float: left;*/
  z-index: 1;
  position: relative;
  /*margin-left: -24px;*/
}
.button-menu-mobile.disable-btn {
}
@media (max-width: 767.98px) {
  /*.button-menu-mobile {*/
  /*  margin: 0 !important;*/
  /*}*/
}

[data-keep-enlarged=true] .navbar-custom {
  /*padding-left: 0;*/
}
[data-keep-enlarged=true] .button-menu-mobile {
  /*margin: 0;*/
}
[data-keep-enlarged=true] .button-menu-mobile.disable-btn {
  /*display: inline-block;*/
}

@media (max-width: 767.98px) {
  /*.navbar-custom {*/
  /*  left: 0 !important;*/
  /*  padding: 0;*/
  /*}*/
  /*.button-menu-mobile.disable-btn {*/
  /*  display: inline-block;*/
  /*}*/
}
@media (max-width: 375px) {
  /*.navbar-custom.topnav-navbar .button-menu-mobile {*/
  /*  width: auto;*/
  /*}*/
}
.nav-user {
  text-align: left !important;
  position: relative;
  background-color: #fafbfd;
  border: 1px solid #f1f3fa;
  min-height: 70px;
}
.navbar-custom .topbar-menu .nav-link {
  padding: 0;
  position: relative;
  color: var(--ct-topbar-item-color);
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  height: var(--ct-topbar-height);
  cursor: pointer;
}

.top-bar-message-container {
  border-radius: 10px;
  border: 1px solid;
  box-shadow: 0 3px 4px rgba(0, 0, 0, 0.1);
}

.top-bar-message-container.blue {
  background-color: #eaf4fc;
  border-color: #b3d9f5;
}

.top-bar-message-container.red {
  background-color: #fdeaea;
  border-color: #f5b3b3;
}

.top-bar-message-container.green {
  background-color: #d4f7e0; /* Brighter green background */
  border-color: #80e0a1; /* More saturated green border */
}

.top-bar-message-container.yellow {
  background-color: #fef9e6;
  border-color: #f5e6b3;
}

.notification-badge {
  margin: 0 0 0 0.25rem;
  top: 2px;
  right: 2px;
  background-color: #dd4a4a;
  color: white;
  font-size: 10px;
  font-weight: bold;
  border-radius: 50%;
  min-width: 18px;
  height: 18px;
  line-height: 18px;
  text-align: center;
  display: inline-block;
  /*padding: 0 4px;*/
}

.notification-bell-unread {
  color: #dd4a4a;
}
