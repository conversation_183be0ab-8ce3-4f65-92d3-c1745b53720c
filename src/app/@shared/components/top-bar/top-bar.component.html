<!-- ========== Topbar Start ========== -->
<div class="navbar-custom" [ngClass]="{'mobile-view': leftSideMenuCollapsed}">
  <div class="topbar container-fluid" style="padding: 0">
    <div class="d-flex align-items-center">

<!--      &lt;!&ndash; Chevron Icon for Sidebar Toggle &ndash;&gt;-->
<!--      <div class="dropdown">-->
<!--        <i-->
<!--          class="fa-regular"-->
<!--          [ngClass]="collapsed ? 'fa-chevrons-right' : 'fa-chevrons-left'"-->
<!--          (click)="toggleCollapse()"-->
<!--          style="cursor: pointer; font-size: 18px;">-->
<!--        </i>-->
<!--      </div>-->


      <!-- Sidebar Menu Toggle Button -->
<!--      <button class="button-toggle-menu" (click)="toggleCollapse()">-->
<!--        <i class="mdi mdi-menu"></i>-->
<!--      </button>-->

      <!-- mobile menu toggle -->
      <button *ngIf="leftSideMenuCollapsed" class="button-menu-mobile" (click)="toggleMobileMenu($event)">
        <i class="mdi mdi-menu"></i>
      </button>

    </div>

    <!-- Topbar Search Form -->
    <div class="content-container d-flex w-100">
      <div class="left-container d-none d-sm-block">
        <ul class="topbar-menu d-flex align-items-center">
            <li class="dropdown d-none d-sm-block">
              <app-company-select />
            </li>
        </ul>
      </div>

      <div *ngIf="topBarSetup && topBarSetup.top_bar_enabled" class="d-flex align-items-center px-2 mx-4 my-1 top-bar-message-container cursor-pointer" [ngbTooltip]="topBarSetup.top_bar_message" [ngClass]="topBarSetup.top_bar_color">
        <div>
          <div id="topBarMessageTitle" class="fw-bold">{{topBarSetup.top_bar_title}}</div>
          <div id="topBarMessageContent" class="font-12">{{topBarSetup.top_bar_message}}</div>
        </div>
      </div>


          <!--  Locale selector   -->
      <div class="right-container ms-auto">
        <ul class="topbar-menu d-flex align-items-center justify-content-center gap-md-3 gap-3">
          <li class="dropdown d-sm-none push">
            <app-company-select />
          </li>
          <li class="dropdown push">
            <a class="nav-link dropdown-toggle arrow-none" data-bs-toggle="dropdown" href="#" role="button" aria-haspopup="false" aria-expanded="false">
              <img [src]="selectedLocale.flag_source" alt="flag" class="me-0 me-sm-1" height="12">
              <span class="align-middle d-none d-lg-inline-block">{{selectedLocale.name}}</span>
              <i class="mdi mdi-chevron-down d-sm-inline-block align-middle d-none"></i>
            </a>

            <div class="dropdown-menu dropdown-menu-end dropdown-menu-animated">
              <a *ngFor="let locale of locales; let index = index"
                 href="javascript:void(0);" class="dropdown-item"
                 (click)="setLocale(index)">
                <img src="{{locale.flag_source}}" alt="item-flag" class="me-1" height="12">
                <span class="align-middle">{{locale.name}}</span>
              </a>
            </div>
          </li>

          <li class="dropdown">
            <a class="nav-link" (click)="toggleNotifications()" style="cursor:pointer; position: relative;">
              <i class="fa-regular fa-bell fa-xl" [ngClass]="{'notification-bell-unread': unreadCount > 0}"></i>
              <span *ngIf="unreadCount > 0" class="notification-badge">{{ unreadCount }}</span>
            </a>
          </li>

          <!--    User profile      -->
          <li class="dropdown">
            <a class="nav-link dropdown-toggle arrow-none nav-user px-2" data-bs-toggle="dropdown" href="#" role="button" aria-haspopup="false" aria-expanded="false">
                    <span class="account-user-avatar">
                      <img *ngIf="user?.profile_image_url" class="avatar-xs rounded-circle" src="{{user?.profile_image_url}}" alt="" width="30" height="30">
                      <img *ngIf="!user?.profile_image_url" class="avatar-xs rounded-circle" src="assets/images/users/no-profile-image.jpeg" alt="" width="30" height="30">
                    </span>
              <span class="d-lg-flex flex-column gap-1 d-none">
                        <h5 class="my-0">{{user?.first_name}}</h5>
                        <h6 class="my-0 fw-normal" *ngIf="user?.last_name">{{user?.last_name}}</h6>
                    </span>
            </a>
            <div class="dropdown-menu dropdown-menu-end dropdown-menu-animated profile-dropdown">
              <!-- item-->
              <a href="javascript:void(0);" class="dropdown-item" (click)="routeToProfile()">
                <i class="mdi mdi-account-circle me-1"></i>
                <span>{{"top-bar.profile-dropdown.profile" | translate}}</span>
              </a>

              <!-- item-->
              <a href="javascript:void(0);" class="dropdown-item" (click)="signOut()">
                <i class="mdi mdi-account-edit me-1"></i>
                <span>{{"top-bar.profile-dropdown.logout" | translate}}</span>
              </a>

              <!-- item-->
              <a href="javascript:void(0);" class="dropdown-item" (click)="gotoTerms()">
                <span>{{"top-bar.profile-dropdown.terms" | translate}}</span>
              </a>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </div>
</div>
<!-- ========== Topbar End ========== -->
