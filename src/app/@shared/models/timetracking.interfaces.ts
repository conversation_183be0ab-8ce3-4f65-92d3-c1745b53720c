import {InternalUserResponse} from "./user.interfaces";
import {ProjectResponse} from "./projects.interfaces";
import {DepartmentResponse} from "./departments.interfaces";

export interface TimeTrackingResponse {
  entry_id: number;
  started_at: Date;
  stopped_at: Date;
  ongoing: boolean;
  duration_in_seconds: number;
  description: string;
  activity_id: number;
  activity_name: string;
  locked: boolean;
  work_order_id: number | null;
  auto_registered: boolean;
  use_in_salary: boolean;
  comment: string;
  planned_time_diff_warning: boolean;
  project: ProjectResponse | null;
  department: DepartmentResponse | null;
  is_pause: boolean;
}

export interface CompanyTimeTrackingResponse {
  user: InternalUserResponse;
  trackings: TrackingEntryResponse[];
  total_tracked_time: number;
  total_assigned_time: number;
}

export interface TimeTrackingOccurrenceResponse {
  [key: string]: number
}

export interface TrackingEntryResponse {
  description: string;
  entry_id: number | null;
  started_at: Date | null;
  stopped_at: Date | null;
  ongoing: number | null;
  duration_in_seconds: number | null;
  work_order_id: number | null;
  execution_at: Date | null;
  execution_to: Date | null;
  work_order_duration_in_seconds: number | null;
  order_number: string | null;
  order_id: number | null;
  child: number;
  activity_id: number;
  activity_name: string;
  locked: boolean;
  use_in_salary: boolean;
  comment: string;
  planned_time_diff_warning: boolean;
  is_pause: boolean;
}

export interface WorkingHoursResponse {
  entry_id: number;
  quantity: number;
  day: number;
  month: number;
  year: number;
  user: InternalUserResponse;
  comment: string;
  activity_id: number;
  activity_name: string;
  salary_type_id: number;
  salary_type_name: string;
  salary_approval_id: number | null;
  rate: number;
  calculation_type_id: number;
  calculation_type_name: string;
  project: ProjectResponse | null;
  department: DepartmentResponse | null;
}


export interface SalaryRuleTypeResponse {
  salary_rule_type_id: number;
  salary_rule_type_name: string;
  description: string;
}

export interface TimeTrackingActivityResponse {
  activity_id: number;
  activity_name: string;
  activity_type_id: number;
  activity_type_name: string;
  default_salary_type_id: number;
  default_salary_type_name: string;
  accounting_id: string | null;
  accounting_name: string | null;
  created_at: Date;
  is_default: boolean;
  generates_salary: boolean;
  index: number;
  color_hex: string;
  uses_vacation_days: boolean;
}

export interface CompanySalaryRuleResponse {
  salary_rule_id: number;
  salary_rule_type_id: number;
  salary_rule_type_name: string;
  salary_type_id: number;
  salary_type_name: string;
  weekdays: number[] | null;
  start_time: string | null;
  end_time: string | null;
  num_hours: number | null;
  active_from: Date | null;
  active_to: Date | null;
  description: string | null;
  created_at: Date;
}

export interface CompanySalaryTypeResponse {
  salary_type_id: number;
  salary_type_name: string;
  calculation_type_id: number;
  calculation_type_name: string;
  value: number | null;
  accounting_id: string | null;
  accounting_name: string | null;
  created_at: Date;
  index: number;
}

export interface TimeTrackingActivityTypeResponse {
  activity_type_id: number;
  activity_type_name: string;
  generates_salary: boolean;
  subDescription: string;
}

export interface WorkingHourPeriodResponse {
  entry_id: number;
  user: InternalUserResponse;
  month: number;
  year: number;
  status_id: number;
  status_name: string;
  label?: string;
  labelLong?: string;
}

export interface WorkingHourPeriodEntryResponse {
  entry_id: number;
  label?: string;
}

export interface UserWorkingHourPeriodResponse {
  user: InternalUserResponse;
  working_hour_periods: WorkingHourPeriodResponse[];
}

export interface UserSalaryTimeTrackingResponse {
  entry_id: number;
  started_at: Date;
  stopped_at: Date;
  description: string;
  ongoing: boolean;
  duration_in_seconds: number;
  comment: string;
  time_tracking_status_id: number;
  time_tracking_status_name: string;
  auto_registered: boolean;
  locked: boolean;
  activity_id: number;
  activity_name: string;
  work_order_id: number;
  project: ProjectResponse | null;
  department: DepartmentResponse | null;
  is_pause: boolean;
  pause_duration_in_seconds: number;
  child_trackings: UserSalaryTimeTrackingResponse[];
}

export interface SalaryApprovalResponse {
  salary_approval_id: number;
  month: number;
  year: number;
  approved_by: InternalUserResponse | null;
  created_by: InternalUserResponse;
  sent_to_accounting_by: InternalUserResponse | null;
  created_at: Date;
  approved_at: Date;
  sent_to_accounting_at: Date;
}

export interface SalaryApprovalAccountingPreCheckResponse {
  users: InternalUserResponse[];
  salary_types: CompanySalaryTypeResponse[];
  activities: TimeTrackingActivityResponse[];
}

export interface TimeTrackingLogResponse {
  changes: string[];
  set_by: InternalUserResponse;
  set_at: Date;
}

export interface SalaryCalculationTypeResponse {
  calculation_type_id: number;
  calculation_type_name: string;
}

export interface EmployeeAbsenceResponse {
  entry_id: number;
  user: InternalUserResponse;
  active_from: Date;
  active_to: Date;
  description: string;
  activity_id: number;
  activity_name: string;
  color_hex: string | null;
  approved_by: InternalUserResponse | null;
  approved_at: Date | null;
  declined_by: InternalUserResponse | null;
  declined_at: Date | null;
  created_at: Date;
}
