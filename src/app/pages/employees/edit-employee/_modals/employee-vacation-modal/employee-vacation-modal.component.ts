import {Component, HostListener, Input, OnInit} from '@angular/core';
import {NgbActiveModal, NgbModal, NgbTooltip} from '@ng-bootstrap/ng-bootstrap';
import {CompanySalaryTypeResponse, TimeTrackingActivityResponse, TimeTrackingActivityTypeResponse} from "../../../../../@shared/models/timetracking.interfaces";
import {TranslateModule} from "@ngx-translate/core";
import {NgIf} from "@angular/common";
import {TrackingService} from "../../../../../@shared/services/tracking.service";

import {InputComponent} from "../../../../../@shared/components/input/input.component";
import {FormControl, Validators} from "@angular/forms";
import {_CRM_EMP_11, _CRM_EMP_13, _CRM_EMP_17, _CRM_EMP_18, _CRM_TTR_17, _CRM_TTR_18, _CRM_TTR_31, _CRM_TTR_32} from "../../../../../@shared/models/input.interfaces";
import {ButtonComponent} from "../../../../../@shared/components/button/button.component";
import {EmployeeSalaryResponse, EmployeeVacationDaysResponse} from "../../../../../@shared/models/employee.interfaces";
import {EmployeeService} from "../../../../../@shared/services/employee.service";
import {DatepickerinoComponent} from "../../../../../@shared/components/datepickerino/datepickerino.component";
import {UtilsService} from "../../../../../@core/utils/utils.service";
import {addDays} from "date-fns";
import {ButtonDoubleComponent} from "../../../../../@shared/components/button-double/button-double.component";
import {StandardImports} from "../../../../../@shared/global_import";

@Component({
    selector: 'app-employee-vacation-modal',
    templateUrl: './employee-vacation-modal.template.html',
    standalone: true,
  imports: [StandardImports, ButtonDoubleComponent, DatepickerinoComponent]
})

export class EmployeeVacationModalComponent implements OnInit {

  @Input() vacationDays: EmployeeVacationDaysResponse | null = null;
  @Input() userId: string;

  initialDaysControl = new FormControl(25, [Validators.required]);
  daysUsedControl = new FormControl(0, [Validators.required]);
  yearControl = new FormControl(new Date().getFullYear(), [Validators.required]);

  loading: boolean = false;

  constructor(public activeModal: NgbActiveModal, private employeeService: EmployeeService, public utilsService: UtilsService) { }

  ngOnInit(): void {
    if (this.vacationDays) {
      this.initialDaysControl.setValue(this.vacationDays.initial_days);
      this.daysUsedControl.setValue(this.vacationDays.days_used);
    }
  }

  save() {
    if (this.initialDaysControl.invalid || this.daysUsedControl.invalid) {
      this.initialDaysControl.markAsTouched();
      this.daysUsedControl.markAsTouched();
      return;
    }

    this.loading = true;

    if (this.vacationDays) {
      let payload: _CRM_EMP_17 = {
        entry_id: this.vacationDays.entry_id,
        user_id: this.userId,
        initial_days: this.initialDaysControl.value!,
        days_used: this.daysUsedControl.value!,
      }
      this.employeeService.updateUserVacationDays(payload).subscribe((res) => {
        this.loading = false;
        this.activeModal.close(res);
      }, (err) => {
        this.loading = false;
      });
    }

    else {
      let payload: _CRM_EMP_18 = {
        year: this.yearControl.value!,
        user_id: this.userId,
        initial_days: this.initialDaysControl.value!,
        days_used: this.daysUsedControl.value!,
      }
      this.employeeService.createUserVacationDays(payload).subscribe((res) => {
        this.loading = false;
        this.activeModal.close(res);
      }, (err) => {
        this.loading = false;
      });
    }
  }

  cancel() {
    this.activeModal.close(false)
  }
}
