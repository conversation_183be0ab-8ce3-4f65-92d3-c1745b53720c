<div class="modal-header d-flex justify-content-between align-items-center">
  <h4 class="text-center" style="flex-grow: 1;">{{ vacationDays ? 'Rediger feriedager for ' + vacationDays.year : 'Opprett feriedager' }}</h4>
</div>
<div class="modal-body row p-3">

  <div class="d-flex gap-2">
    <app-input
      *ngIf="!vacationDays"
      [labelKey]="'År'"
      [control]="yearControl"
    ></app-input>

    <app-input
      [labelKey]="'Dager totalt'"
      [control]="initialDaysControl"
    ></app-input>

    <app-input
      [labelKey]="'Dager brukt'"
      [control]="daysUsedControl"
    ></app-input>
  </div>

</div>

<div class="modal-footer justify-content-end pe-2 gap-2">
  <app-button
    [translationKey]="'common.save'"
    [loading]="loading"
    [disabled]="this.daysUsedControl.invalid || this.initialDaysControl.invalid"
    (buttonClick)="save()"
  ></app-button>
  <app-button
    [translationKey]="'common.cancel'"
    (buttonClick)="cancel()"
  ></app-button>
</div>
