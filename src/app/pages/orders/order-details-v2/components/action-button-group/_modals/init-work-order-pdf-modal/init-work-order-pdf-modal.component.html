<div class="modal-header d-flex justify-content-between align-items-center" tabindex="1">
  <i class="fa-regular fa-xmark fa-xl" (click)="activeModal.close()" style="font-weight: 400; cursor: pointer"></i>
  <h4 class="text-center" style="flex-grow: 1;">{{ "Print arbeidsordre" | translate }}</h4>
</div>

<div class="modal-body p-3">
  <div class="d-flex flex-column gap-2">

    <div *ngFor="let wo of setup.workOrders" class="" style="border: 1px solid #E0E0E0; border-radius: 8px;">
      <div class="d-flex justify-content-between align-items-center p-2">
        <div class="d-flex flex-column">
          <h5 class="my-0">#{{wo.work_order_number}} - {{ wo.work_order_title }}</h5>
        </div>
        <app-toggle-switch
          [bigCheck]="true"
          [useCheckbox]="true"
          [state]="wo.include"
          (stateChange)="wo.include = $event"
        ></app-toggle-switch>
      </div>

    </div>

    <app-toggle-switch
      [labelKey]="'Vis jobbeskrivelse'"
      [bigCheck]="true"
      [useCheckbox]="true"
      [state]="setup.showDescription"
      (stateChange)="setup.showDescription = $event"
    ></app-toggle-switch>
    <app-toggle-switch
      [labelKey]="'Vis kundenotater'"
      [bigCheck]="true"
      [useCheckbox]="true"
      [state]="setup.showCustomerNotes"
      (stateChange)="setup.showCustomerNotes = $event"
    ></app-toggle-switch>
    <app-toggle-switch
      [labelKey]="'Vis interne notater'"
      [bigCheck]="true"
      [useCheckbox]="true"
      [state]="setup.showInternalNotes"
      (stateChange)="setup.showInternalNotes = $event"
    ></app-toggle-switch>
    <app-toggle-switch
      [labelKey]="'Vis sjekklister'"
      [bigCheck]="true"
      [useCheckbox]="true"
      [state]="setup.showTasks"
      (stateChange)="setup.showTasks = $event"
    ></app-toggle-switch>
    <app-toggle-switch
      [labelKey]="'Vis kundespørsmål'"
      [bigCheck]="true"
      [useCheckbox]="true"
      [state]="setup.showCustomerQuestions"
      (stateChange)="setup.showCustomerQuestions = $event"
    ></app-toggle-switch>
    <app-toggle-switch
      [labelKey]="'Vis adresser'"
      [bigCheck]="true"
      [useCheckbox]="true"
      [state]="setup.showAddresses"
      (stateChange)="toggleShowAddresses($event)"
    ></app-toggle-switch>
    <app-toggle-switch
      [labelKey]="'Vis ekstra adresseinformasjon'"
      [bigCheck]="true"
      [useCheckbox]="true"
      [isDisabled]="!setup.showAddresses"
      [state]="setup.showAddressInformation"
      (stateChange)="setup.showAddressInformation = $event"
    ></app-toggle-switch>
    <app-toggle-switch
      [labelKey]="'Vis ordrelinjer'"
      [bigCheck]="true"
      [useCheckbox]="true"
      [state]="setup.showOrderLines"
      (stateChange)="toggleOrderLines($event)"
    ></app-toggle-switch>
    <app-toggle-switch
      [labelKey]="'Vis priser'"
      [bigCheck]="true"
      [useCheckbox]="true"
      [isDisabled]="!setup.showOrderLines"
      [state]="setup.showPrices"
      (stateChange)="setup.showPrices = $event"
    ></app-toggle-switch>
    <app-toggle-switch
      [labelKey]="'Legg til signaturfelt'"
      [bigCheck]="true"
      [useCheckbox]="true"
      [state]="setup.showSignature"
      (stateChange)="setup.showSignature = $event"
    ></app-toggle-switch>

  </div>

</div>

<div class="modal-footer pe-3">
  <app-button
    [translationKey]="'common.cancel'"
    [themeStyle]="'secondary'"
    (buttonClick)="activeModal.close()"
  ></app-button>
  <app-button
    [translationKey]="'common.create'"
    [loading]="loading"
    (buttonClick)="create()"
  ></app-button>


</div>

<app-work-order-pdf
  *ngIf="order && renderWorkOrderPdf"
  class="offscreen-quote-pdf"
  [order]="order"
  [company]="company"
  [setup]="setup"
  (pdfDownloaded)="onPdfDownloaded()"
></app-work-order-pdf>
