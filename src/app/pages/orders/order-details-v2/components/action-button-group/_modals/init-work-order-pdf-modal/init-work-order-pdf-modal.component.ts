import { Component, Input, OnInit } from '@angular/core';
import { TranslateService } from "@ngx-translate/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import {OrderResponse, WorkOrderResponse} from "../../../../../../../@shared/models/order.interfaces";
import { OrderService } from "../../../../../../../@shared/services/order.service";
import { ToastService } from 'src/app/@core/services/toast.service';
import {StandardImports} from "../../../../../../../@shared/global_import";
import {ToggleSwitchComponent} from "../../../../../../../@shared/components/toggle-switch/toggle-switch.component";
import {WorkOrderPdfComponent} from "../../../work-order-pdf/work-order-pdf.component";
import {CompanyResponse} from "../../../../../../../@shared/models/company.interfaces";
import {CompanyService} from "../../../../../../../@shared/services/company.service";
import {UnitDetails} from "../../../../../../../@shared/models/input.interfaces";

export interface PdfWorkOrder extends WorkOrderResponse {
  include: boolean;
  chunkedAddresses: UnitDetails[][];
}

export interface WorkOrderPdfSetup {
  workOrders: PdfWorkOrder[];
  showDescription: boolean;
  showCustomerNotes: boolean;
  showInternalNotes: boolean;
  showOrderLines: boolean;
  showPrices: boolean;
  showSignature: boolean;
  showTasks: boolean;
  showCustomerQuestions: boolean;
  showAddresses: boolean;
  showAddressInformation: boolean;
}

@Component({
    selector: 'app-init-work-order-modal',
    templateUrl: './init-work-order-pdf-modal.component.html',
    styleUrls: ['./init-work-order-pdf-modal.component.scss'],
    standalone: true,
  imports: [StandardImports, ToggleSwitchComponent, WorkOrderPdfComponent]
})
export class InitWorkOrderPdfModalComponent implements OnInit {
  @Input() order: OrderResponse;
  workOrders: WorkOrderResponse[];
  company: CompanyResponse;
  renderWorkOrderPdf: boolean = false;
  loading: boolean = false;
  setup: WorkOrderPdfSetup = {
    workOrders: [],
    showDescription: true,
    showCustomerNotes: true,
    showInternalNotes: true,
    showOrderLines: true,
    showPrices: true,
    showSignature: true,
    showTasks: true,
    showCustomerQuestions: true,
    showAddresses: true,
    showAddressInformation: true,
  }

  constructor(private translate: TranslateService,
              private orderService: OrderService,
              public activeModal: NgbActiveModal,
              private toastService: ToastService,
              private companyService: CompanyService,
  ) {
  }

  ngOnInit() {
    this.workOrders = this.order.work_orders;
    this.setup.workOrders = this.workOrders.map((workOrder) => {
      return {
        ...workOrder,
        include: true,
        chunkedAddresses: this.chunkAddresses(workOrder.addresses),
      };
    })
  }

  chunkAddresses(addresses: UnitDetails[]): UnitDetails[][] {
    const chunkSize = 2;
    const chunks: UnitDetails[][] = [];

    for (let i = 0; i < addresses.length; i += chunkSize) {
      chunks.push(addresses.slice(i, i + chunkSize));
    }
    return chunks;
  }

  toggleOrderLines(value: boolean) {
    this.setup.showOrderLines = value;
    if (!this.setup.showOrderLines) {
      this.setup.showPrices = false;
    }
  }

  toggleShowAddresses(value: boolean) {
    this.setup.showAddresses = value;
    if (!this.setup.showAddresses) {
      this.setup.showAddressInformation = false;
    }
  }

  create() {
    this.companyService.getCompanyData().subscribe((res: CompanyResponse) => {
      this.company = res;
      this.renderWorkOrderPdf = true;
      this.loading = true;
    });
    // this.activeModal.close(this.setup);
  }

  onPdfDownloaded() {
    this.loading = false;
    this.activeModal.close();
  }
}

