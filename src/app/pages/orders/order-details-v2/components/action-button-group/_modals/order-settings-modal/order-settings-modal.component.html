<div class="modal-header d-flex justify-content-between align-items-center" tabindex="1">
  <i class="fa-regular fa-xmark fa-xl" (click)="activeModal.close()" style="font-weight: 400; cursor: pointer"></i>
  <h4 class="text-center" style="flex-grow: 1;">{{ "Innstillinger for ordren" | translate }}</h4>
</div>

<div class="modal-body p-3">
  <div class="d-flex flex-column gap-2">
    <app-toggle-switch
      [labelKey]="'settings.company.requireCrewWorkOrderConfirmation'"
      [bigSwitch]="true"
      [state]="order.require_crew_work_order_confirmation"
      (stateChange)="toggleRequireCrewAcceptance($event)"
    ></app-toggle-switch>

    <app-toggle-switch
      [labelKey]="'orders.orderDetails.enableProject'"
      [bigSwitch]="true"
      [state]="order.is_project"
      (stateChange)="toggleProjectView($event)"
    ></app-toggle-switch>

  </div>

</div>

<div class="modal-footer pe-3">
  <app-button
    [translationKey]="'common.close'"
    [themeStyle]="'secondary'"
    (buttonClick)="activeModal.close()"
  ></app-button>


</div>
