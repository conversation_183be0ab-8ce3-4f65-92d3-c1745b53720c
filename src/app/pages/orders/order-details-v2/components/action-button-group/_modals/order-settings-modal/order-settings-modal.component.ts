import { Component, Input, OnInit } from '@angular/core';
import { TranslateService } from "@ngx-translate/core";
import { NgbActiveModal } from "@ng-bootstrap/ng-bootstrap";
import {OrderResponse, WorkOrderResponse} from "../../../../../../../@shared/models/order.interfaces";
import { OrderService } from "../../../../../../../@shared/services/order.service";
import { ToastService } from 'src/app/@core/services/toast.service';
import {StandardImports} from "../../../../../../../@shared/global_import";
import {ToggleSwitchComponent} from "../../../../../../../@shared/components/toggle-switch/toggle-switch.component";
import {WorkOrderPdfComponent} from "../../../work-order-pdf/work-order-pdf.component";
import {CompanyResponse} from "../../../../../../../@shared/models/company.interfaces";
import {CompanyService} from "../../../../../../../@shared/services/company.service";
import {_CRM_ORD_97, UnitDetails} from "../../../../../../../@shared/models/input.interfaces";


@Component({
    selector: 'order-settings-modal',
    templateUrl: './order-settings-modal.component.html',
    styleUrls: ['./order-settings-modal.component.scss'],
    standalone: true,
  imports: [StandardImports, ToggleSwitchComponent, WorkOrderPdfComponent]
})
export class OrderSettingsModalComponent implements OnInit {
  @Input() order: OrderResponse;

  constructor(private translate: TranslateService,
              private orderService: OrderService,
              public activeModal: NgbActiveModal,
              private toastService: ToastService,
              private companyService: CompanyService,
  ) {
  }

  ngOnInit() {
  }

  toggleRequireCrewAcceptance(state: boolean) {
    let payload: _CRM_ORD_97 = {
      order_id: this.order.order_id,
      require_crew_work_order_confirmation: state
    }
    this.orderService.patchOrder(payload).subscribe({
      next: (response: OrderResponse) => {
        this.order = response;
        this.orderService.updateOrder(response, 'order-settings-modal');
        this.orderService.workOrderListUpdated$.next();
        this.toastService.successToast('updated');
      }
    });
  }

  toggleProjectView(state: boolean) {
    let payload: _CRM_ORD_97 = {
      order_id: this.order.order_id,
      is_project: state
    }
    this.orderService.patchOrder(payload).subscribe({
      next: (response: OrderResponse) => {
        this.order = response;
        this.orderService.updateOrder(response, 'order-settings-modal');
        this.toastService.successToast('updated');
      }
    });
  }

}

