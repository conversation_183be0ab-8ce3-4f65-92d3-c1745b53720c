<app-card
  [labelKey]="'orderDetails.customerFeedback.title'"
  [padding]="'0'"
  [buttonHeader]="true">

  <div buttonHeader>
    <app-button [customClass]="'me-2 p-0'" [translationKey]="'common.redigate'" [buttonType]="'link'" (buttonClick)="AddCustomerQuestionTemplate()"></app-button>
  </div>

  <ng-container cardcontent>
    <div class="customer-card-padding">
      <div class="fw-bold mb-1">{{ "orderDetails.customerFeedback.questionnaire" | translate }}</div>
      <div *ngIf="!isQuestionAnswered"> {{ "orderDetails.customerFeedback.questionnaire.notAnswered" | translate }}
      </div>
      <div *ngIf="isQuestionAnswered">
        <div class="d-flex">
          <div><i class="fa-regular fa-square-check fa-3x" style="color: #448C74;"></i></div>
          <div class="col-8 ps-2">{{ "orderDetails.customerFeedback.questionnaire.Answered" | translate }}</div>
        </div>
        <div class="text-center">
          <app-button [buttonType]="'link'" [small]="true" [translationKey]="'orderDetails.customerFeedback.questionnaire.button'" (buttonClick)="openModal()"></app-button>
        </div>
      </div>
    </div>

  </ng-container>
</app-card>
