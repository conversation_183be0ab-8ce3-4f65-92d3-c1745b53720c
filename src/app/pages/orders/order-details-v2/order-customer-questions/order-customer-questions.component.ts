import {Component, Input, OnInit} from '@angular/core';
import {
  OrderLineResponse,
  OrderResponse, WorkOrderResponse
} from "../../../../@shared/models/order.interfaces";

import {NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {SpecificationsModalComponent} from "./_modal/specifications-modal/specifications-modal.component";
import {OrderService} from "../../../../@shared/services/order.service";
import {CommonModule} from "@angular/common";
import {CardComponent} from "../../../../@shared/components/layout/card/card.component";
import {ButtonComponent} from "../../../../@shared/components/button/button.component";
import {TranslateModule} from "@ngx-translate/core";
import {takeUntil} from "rxjs/operators";
import {Subject} from "rxjs";
import {LinkTaskTemplateComponent} from "../order-checklist/_modals/link-task-template/link-task-template.component";
import {
  LinkCustomerQuestionTemplateComponent
} from "./_modal/link-customer-question-template/link-customer-question-template.component";
import {StandardImports} from "../../../../@shared/global_import";

@Component({
  selector: 'app-order-customer-questions',
  templateUrl: './order-customer-questions.component.html',
  styleUrls: ['./order-customer-questions.component.css'],
  standalone: true,
  imports: [StandardImports, CardComponent]
})
export class OrderCustomerQuestionsComponent implements OnInit {
  @Input() workOrderView: boolean = false;
  workOrder?: WorkOrderResponse;
  order?: OrderResponse
  isQuestionAnswered: boolean

  destroy$ = new Subject<void>();

  constructor(private modalService: NgbModal, private orderService: OrderService) {}


  ngOnInit(){
    if (!this.workOrderView) {
      this.orderService.order$.pipe(takeUntil(this.destroy$)).subscribe(order => {
        this.order = order;
        if(this.order) {
          this.initFeedback();
        }
      });
    } else {
      this.orderService.workOrder$.subscribe(workOrder => {
        this.destroy$.next();
        this.workOrder = workOrder;
        this.initFeedback();
      })
    }
  }

  initFeedback(){
    this.order?.customer_questions.forEach((question) => {
      if (question.answered === 1) {
        this.isQuestionAnswered = true;
      }
    });
  }

  openModal(){
    const modalRef= this.modalService.open(SpecificationsModalComponent, {size: 'md'});
    modalRef.componentInstance.order = this.order;
  }

  AddCustomerQuestionTemplate(){
    const modalRef= this.modalService.open(LinkCustomerQuestionTemplateComponent, {size: 'lg'});
    modalRef.componentInstance.order = this.order;
    modalRef.componentInstance.workOrder = this.workOrder;
  }
}
