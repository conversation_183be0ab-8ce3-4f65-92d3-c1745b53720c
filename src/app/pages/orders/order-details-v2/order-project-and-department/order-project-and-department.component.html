<app-card
  [labelKey]="!departmentsEnabled ? 'orderDetails.projectsAndDepartments.title.projectOnly' : 'orderDetails.projectsAndDepartments.title'"
  [padding]="'0'"
  [buttonHeader]="false">
  <div class="px-3 pt-2 pb-2" cardcontent>
    <div class="mb-2">
      <label>{{'orderDetails.projectsAndDepartments.project' | translate}}</label>
      <app-selectorini
        [placeholderTranslationKey]="'orderDetails.projectsAndDepartments.project.placeholder'"
        [selectedItem]="project"
        [loading]="projectLoading"
        [searchMainDisplayKeys]="['display']"
        [selectedMainDisplayKeys]="['project_name']"
        [searchOnFocus]="true"
        [clearComponent]="clearSelectoriniObservable"
        [searchFunction]="integrationService.getAccountingProjects.bind(this.integrationService)"
        (itemSelectedEmitter)="projectSelected($event)"
        (itemDeselectedEmitter)="deselectProject()"
      ></app-selectorini>
    </div>
    <div *ngIf="departmentsEnabled" class="">
      <label>{{'orderDetails.projectsAndDepartments.department' | translate}}</label>
      <app-selectorini
        [placeholderTranslationKey]="'orderDetails.projectsAndDepartments.department.placeholder'"
        [selectedItem]="department"
        [loading]="departmentLoading"
        [searchMainDisplayKeys]="['display']"
        [selectedMainDisplayKeys]="['department_name']"
        [searchOnFocus]="true"
        [clearComponent]="clearSelectoriniObservable"
        [searchFunction]="integrationService.getAccountingDepartments.bind(this.integrationService)"
        (itemSelectedEmitter)="departmentSelected($event)"
        (itemDeselectedEmitter)="deselectDepartment()"
      ></app-selectorini>
    </div>
  </div>
</app-card>
