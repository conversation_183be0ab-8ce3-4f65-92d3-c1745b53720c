import {Component, OnInit, TemplateRef, ViewChild} from '@angular/core';
import {ProductService} from "../../../@shared/services/product.service";
import {ProductBaseResponse} from "../../../@shared/models/product.interfaces";
import {PaginationResponse} from "../../../@shared/models/response.interfaces";
import {NgbModal, NgbModalRef} from "@ng-bootstrap/ng-bootstrap";
import {CreateProductModalComponent} from "./_modals/create-product-modal/create-product-modal.component";
import {BehaviorSubject, pairwise, Subject} from "rxjs";
import {PaginationContainer} from "../../../@shared/models/global.interfaces";
import {ImportProductsModalComponent} from "./_modals/import-products-modal/import-products-modal.component";
import {TablerinoColumn, TablerinoSettings} from "../../../@shared/components/tablerino/tablerino.component";
import {WorkOrderCompactResponse} from "../../../@shared/models/order.interfaces";
import {HeaderFiltersContainer} from "../../../@shared/components/tablerino-header/tablerino-header.component";
import {currencyFormat, formatTimeYMD} from "../../../@core/utils/utils.service";
import {StorageService} from "../../../@core/services/storage.service";
import {_CRM_ORD_170, _CRM_PRD_1, _CRM_PRD_57} from "../../../@shared/models/input.interfaces";
import {takeUntil} from "rxjs/operators";
import {Router} from "@angular/router";
import {StandardImports} from "../../../@shared/global_import";
import {PageHeaderComponent} from "../../../@shared/components/page-header/page-header.component";
import {TablerinoCompleteComponent} from "../../../@shared/components/tablerino-complete/tablerino-complete.component";
import {DeleteButtonComponent} from "../../../@shared/components/delete-button/delete-button.component";

@Component({
    selector: 'app-product-overview',
    templateUrl: './product-overview.component.html',
    styleUrls: ['./product-overview.component.css'],
    standalone: true,
  imports: [StandardImports, PageHeaderComponent, TablerinoCompleteComponent, DeleteButtonComponent]
})
export class ProductOverviewComponent implements OnInit {
  columnsSubject: BehaviorSubject<TablerinoColumn[]> = new BehaviorSubject<TablerinoColumn[]>([]);
  products: PaginationResponse<ProductBaseResponse[]>;
  productRows: ProductBaseResponse[] = [];
  settings: TablerinoSettings = {
    checkboxes: false,
    clickableRows: true,
    routerLinkKey: 'product_id',
    routerLinkPrefix: '/products/'
  }
  loading: boolean = false;
  dataResponse: PaginationResponse<WorkOrderCompactResponse[]>;
  paginationSubject: BehaviorSubject<PaginationContainer> = new BehaviorSubject<PaginationContainer>({page: 1, limit: 25, paginate: 1, totalPages: 0, totalItems: 0});
  headerFiltersContainerSubject: BehaviorSubject<HeaderFiltersContainer> = new BehaviorSubject<HeaderFiltersContainer>({filters: [], init: true});
  cancelCurrentRequest$ = new Subject<void>();

  operateExVat: boolean = false;
  accountingEnabled: boolean = false;

  @ViewChild('deleteDiv', { static: true }) deleteDiv!: TemplateRef<any>;

  constructor(private router: Router, private productService: ProductService, private storageService: StorageService, private modalService: NgbModal) { }

  ngOnInit(): void {
    this.storageService.accountingEnabled$.subscribe((enabled) => {
      if (enabled) {
        this.accountingEnabled = enabled;
      }
    });

    this.storageService.operateExVat$.subscribe((operateExVat) => {
      this.operateExVat = operateExVat;
    });
    this.initializeColumns();
    this.fetch();
    this.paginationSubject.pipe(pairwise()).subscribe((paginationDetails) => {
      if (paginationDetails[0].page !== paginationDetails[1].page) {
        this.fetch();
      }
    });
    this.headerFiltersContainerSubject.subscribe((headerFilters) => {
      if (!headerFilters.init) {
        this.fetch();
      }
    });
  }

  initializeColumns() {
    this.columnsSubject.next([
      {
        name: 'product_name',
        labelKey: 'common.name',
        formatter: (prd: ProductBaseResponse) => prd.product_name,
        sort: true,
        visible: true,
      },
      {
        name: 'created_at',
        labelKey: 'product-list.dateAdded',
        formatter: (prd: ProductBaseResponse) => formatTimeYMD(prd.created_at).split(' ')[0],
        sort: true,
        visible: true,
      },
      {
        name: '_price_inc_vat',
        labelKey: 'product-list.price',
        formatter: (p: ProductBaseResponse) => currencyFormat((this.operateExVat ? p.price_ex_vat : p.price_inc_vat), false),
        sort: true,
        visible: true,
      },
      {
        name: 'delete_button',
        labelKey: 'actions',
        formatter: (p: ProductBaseResponse) => '',
        sort: false,
        visible: true,
        ngTemplate: this.deleteDiv,
        hideHeaderName: true,
        buttonColumn: true,
      },
    ]);
  }

  fetch(searchString: string = '') {
     this.cancelCurrentRequest$.next();
    this.loading = true;

    let sortColumn = this.columnsSubject.value.find(col => col.sortedAsc || col.sortedDesc) || null;
    let sortKey: string = sortColumn?.name || 'product_name';
    let sortDirection: 'asc' | 'desc' = sortColumn ? (sortColumn.sortedAsc ? 'asc' : 'desc') : 'asc';

    let params: _CRM_PRD_57 = {
      paginate: 1,
      page: this.paginationSubject.value.page,
      limit: this.paginationSubject.value.limit,
      order_by: sortKey,
      order_direction: sortDirection,
      search_string: searchString,
      search_string_columns: ['product_name'],
    }

    this.productService.getAllProductsBaseData(params).pipe(takeUntil(this.cancelCurrentRequest$)).subscribe((res) => {
      this.products = res;
      this.productRows = res.data;
      this.paginationSubject.next({
        ...this.paginationSubject.value,
        totalItems: res.total_items,
        totalPages: res.total_pages
      });
      this.loading = false;
    }, error => {
      this.loading = false
    });
  }

  openImportProductsModal(accounting: boolean) {
    if (accounting) {
      let modalRef = this.modalService.open(ImportProductsModalComponent, {size: 'lg'});
      modalRef.result.then((result) => {
        if (result) {
          this.fetch();
        }
      });
    }
  }

  rowClicked(event: ProductBaseResponse | any){
    this.router.navigateByUrl("/products/" + event.product_id);
  }

  deleteProduct(product: ProductBaseResponse){
    this.productService.deleteProduct(product.product_id).subscribe(() => {
      this.fetch();
    });
  }

  openAddProductModal(){
    const modalRef = this.modalService.open(CreateProductModalComponent, {size: 'md'});
  }
}
