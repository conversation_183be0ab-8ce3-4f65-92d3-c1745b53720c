import {Component, OnInit} from '@angular/core';
import {NgbActiveModal, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {FloorResponse, RoomResponse, TemplatePrerequisiteResponse, TemplateResponse} from "../../../../../../@shared/models/reportinator.interfaces";
import {ReportinatorService} from "../../../../../../@shared/services/reportinator.service";
import {_CRM_ORD_31, _REP_TMP_10, _REP_TMP_13, _REP_TMP_7} from "../../../../../../@shared/models/input.interfaces";
import {CdkDrag, CdkDragHandle, CdkDropList, moveItemInArray} from "@angular/cdk/drag-drop";
import {FormControl, Validators} from "@angular/forms";
import {StandardImports} from "../../../../../../@shared/global_import";
import {SpinnerComponent} from "../../../../../../@shared/components/spinner/spinner.component";
import {DeleteButtonComponent} from "../../../../../../@shared/components/delete-button/delete-button.component";


@Component({
    selector: 'template-descriptions-modal',
    templateUrl: './descriptions-modal.template.html',
    styleUrls: ['./descriptions-modal.styles.css'],
    standalone: true,
  imports: [StandardImports, SpinnerComponent, CdkDropList, CdkDrag, CdkDragHandle, DeleteButtonComponent]
})
export class DescriptionsModalComponent implements OnInit {
  template: TemplateResponse;
  controls: Map<string, FormControl> = new Map<string, FormControl>();
  loading: boolean = false;
  keyNames: {[key: string]: string} = {
    'report_description': 'Om rapporten',
    'area_description': 'Informasjon om arealbeskrivelse',
    'tg_general_description': 'Generell informasjon om tilstandsgrad',
    'tgiu_description': 'Tilstandsgrad IU',
    'tg0_description': 'Tilstandsgrad 0',
    'tg1_description': 'Tilstandsgrad 1',
    'tg2_description': 'Tilstandsgrad 2',
    'tg3_description': 'Tilstandsgrad 3',
    'cost_estimate_description': 'Kostnadsestimat',
    'upgrades_description': 'Oppgraderinger'
  }
  collapsedStatus: {[key: string]: boolean} = {
    'report_description': true,
    'area_description': true,
    'tg_general_description': true,
    'tgiu_description': true,
    'tg0_description': true,
    'tg1_description': true,
    'tg2_description': true,
    'tg3_description': true,
    'cost_estimate_description': true,
    'upgrades_description': true
  }

  constructor(public activeModal: NgbActiveModal, private reportinatorService: ReportinatorService, private modalService: NgbModal) {
  }

  ngOnInit() {
    this.controls.set('report_description', new FormControl('', [Validators.maxLength(2500)]));
    this.controls.set('area_description', new FormControl('', [Validators.maxLength(460)]));
    this.controls.set('tg_general_description', new FormControl('', [Validators.maxLength(320)]));
    this.controls.set('tgiu_description', new FormControl('', [Validators.maxLength(460)]));
    this.controls.set('tg0_description', new FormControl('', [Validators.maxLength(460)]));
    this.controls.set('tg1_description', new FormControl('', [Validators.maxLength(460)]));
    this.controls.set('tg2_description', new FormControl('', [Validators.maxLength(460)]));
    this.controls.set('tg3_description', new FormControl('', [Validators.maxLength(460)]));
    this.controls.set('cost_estimate_description', new FormControl('', [Validators.maxLength(320)]));
    this.controls.set('upgrades_description', new FormControl('', [Validators.maxLength(400)]));

    this.reportinatorService.template$.subscribe(template => {
      this.template = template;
      this.controls.get('report_description')?.setValue(template.report_description);
      this.controls.get('area_description')?.setValue(template.area_description);
      this.controls.get('tg_general_description')?.setValue(template.tg_general_description);
      this.controls.get('tgiu_description')?.setValue(template.tgiu_description);
      this.controls.get('tg0_description')?.setValue(template.tg0_description);
      this.controls.get('tg1_description')?.setValue(template.tg1_description);
      this.controls.get('tg2_description')?.setValue(template.tg2_description);
      this.controls.get('tg3_description')?.setValue(template.tg3_description);
      this.controls.get('cost_estimate_description')?.setValue(template.cost_estimate_description);
      this.controls.get('upgrades_description')?.setValue(template.upgrades_description);
    });
  }

  isCollapsed(key: string) {
    return this.collapsedStatus[key];
  }

  save() {
    this.loading = true;
    this.reportinatorService.updateTemplate({
      template_id: this.template.template_id,
      report_description: this.controls.get('report_description')?.value,
      area_description: this.controls.get('area_description')?.value,
      tg_general_description: this.controls.get('tg_general_description')?.value,
      tgiu_description: this.controls.get('tgiu_description')?.value,
      tg0_description: this.controls.get('tg0_description')?.value,
      tg1_description: this.controls.get('tg1_description')?.value,
      tg2_description: this.controls.get('tg2_description')?.value,
      tg3_description: this.controls.get('tg3_description')?.value,
      cost_estimate_description: this.controls.get('cost_estimate_description')?.value,
      upgrades_description: this.controls.get('upgrades_description')?.value
    }).subscribe(() => {
      this.loading = false;
      this.activeModal.close();
    });
  }

  controlsInvalid() {
    for (const control of this.controls.values()) {
      if (control.invalid) {
        return true;
      }
    }
    return false;
  }

  controlInvalid(key: string) {
    return this.controls.get(key)?.invalid;
  }

  cancel() {
    this.activeModal.close();
  }

}
