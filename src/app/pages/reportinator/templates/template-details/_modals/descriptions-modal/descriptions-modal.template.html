<div class="modal-header d-flex justify-content-between align-items-center">
  <h4 class="text-center me-2" style="flex-grow: 1;">{{ 'Beskrivelser' | translate }}</h4>
  <app-spinner *ngIf="loading"></app-spinner>
</div>
<div class="modal-body row p-3">

  <div *ngFor="let descriptionType of controls.keys()" class="accordion-item mb-3 px-0" style="border: 1px solid rgb(222, 226, 230); border-radius: 8px;">
    <div class="accordion-header col-12" [id]="'panelsStayOpen-headingOne' + descriptionType">
      <div
        class="accordion-button col-12"
        type="button"
        data-bs-toggle="collapse"
        [attr.data-bs-target]="'#panelsStayOpen-collapseOne' + descriptionType"
        [attr.aria-expanded]="isCollapsed(descriptionType) ? 'false' : 'true'"
        [attr.aria-controls]="'panelsStayOpen-collapseOne' + descriptionType">
        <div class="d-flex justify-content-between align-items-center col-12 p-2">
          <h5>{{keyNames[descriptionType]}}</h5>
          <i class="fa-solid fa-chevron-down"></i>
        </div>
      </div>
    </div>
    <div
      [id]="'panelsStayOpen-collapseOne' + descriptionType"
      class="accordion-collapse collapse"
      [ngClass]="{'show': !isCollapsed(descriptionType)}"
      [attr.aria-labelledby]="'panelsStayOpen-headingOne' + descriptionType">
      <app-input
        [editMode]="true"
        [textArea]="true"
        [control]="controls.get(descriptionType)!"
        [textAreaRows]="10"
        [textAreaAllowScroll]="true"
      ></app-input>
      <div *ngIf="controlInvalid(descriptionType)" class="p-1 text-danger">Teksten er for lang.</div>
    </div>
  </div>
</div>

<div class="modal-footer justify-content-between pe-2">
  <button class="btn btn-secondary me-2" style="min-width: 80px;" (click)="cancel()">
    {{ 'common.cancel' | translate}}
  </button>

  <app-button
    [translationKey]="'common.save'"
    [loading]="loading"
    [disabled]="controlsInvalid()"
    (buttonClick)="save()"
  ></app-button>
</div>
