<div class="modal-header d-flex justify-content-between align-items-center">
  <h4 class="text-center" style="flex-grow: 1;">{{user.first_name}} {{'salary.absence.conflictModal.title' | translate}}</h4>
</div>

<div class="modal-body p-3">
  <div class="mb-3 text-muted">
    {{'salary.absence.conflictModal.description' | translate:{ name: user.first_name } }}
  </div>
  <div class="" style="max-height: 400px; overflow: scroll;">
    <app-tablerino
      #workOrderTablerino
      [tableName]="'absence-conflict-jobs'"
      [tableData]="itemRows"
      [loading]="tableLoading"
      [columnsSubject]="columnsSubject"
      [selectedRowsSubject]="selectedRowsSubject"
      [settings]="tableSettings"
      (rowClickedEmitter)="rowClicked($event)"
    ></app-tablerino>
  </div>

  <div class="d-flex mt-2">
    <div class="col-3">
      <div class="mb-2">
        <app-profiled-item-list
          [itemIdKey]="'user_id'"
          [employeeSelector]="true"
          [showNoneSelected]="(selectedSubstitutes.length > 0)"
          [initialsKeys]="['full_name']"
          [itemImageKey]="'profile_image_url'"
          [selectedItems]="selectedSubstitutes"
          (selectedItemsUpdatedEmitter)="updateSelectedSubstitutes($event)"
        ></app-profiled-item-list>
      </div>
      <div>
        <app-selectorini
          [zIndex]="1"
          [itemIdKey]="'user_id'"
          [multiSelect]="true"
          [selectedItems]="selectedSubstitutes"
          [showMagnifyingGlass]="false"
          [searchMainDisplayKeys]="['full_name']"
          [labelClass]="'text-dark'"
          [placeholderTranslationKey]="'salary.absence.conflictModal.substitute.placeholder'"
          [initialsKeys]="['full_name']"
          [itemImageKey]="showEmployeeInitials ? '' : 'profile_image_url'"
          [predefinedSearchResults]="employees"
          [predefinedSearchKeys]="['full_name']"
          [maxWidthPercentage]="20"
          (selectedItemsUpdatedEmitter)="updateSelectedSubstitutes($event)"
        ></app-selectorini>
      </div>

    </div>
    <div *ngIf="selectedSubstitutes.length > 0" class="col-6 d-flex ms-2 gap-2 align-items-end">
      <app-button
        [translationKey]="'salary.absence.conflictModal.substitute.add'"
        [loading]="assignmentLoading"
        [disabled]="!anySelected"
        [iconClass]="'fa-regular fa-circle-info ms-1'"
        [iconNgbTooltipTranslationKey]="'salary.absence.conflictModal.substitute.add.tooltip'"
        [iconPlacement]="'right'"
        (buttonClick)="assignUser(false)"
      ></app-button>

      <app-button
        [translationKey]="'salary.absence.conflictModal.substitute.replace'"
        [themeStyle]="'secondary'"
        [loading]="assignmentLoading"
        [disabled]="!anySelected"
        [iconClass]="'fa-regular fa-circle-info ms-1'"
        [iconNgbTooltipTranslationKey]="'salary.absence.conflictModal.substitute.replace.tooltip'"
        [iconPlacement]="'right'"
        (buttonClick)="assignUser(true)"
      ></app-button>
    </div>

    <div *ngIf="selectedSubstitutes.length == 0" class="col ms-2 d-flex align-items-end justify-content-end">
      <app-button
        [translationKey]="'salary.absence.conflictModal.substitute.remove' | translate:{ name: user.first_name }"
        [themeStyle]="'danger'"
        [loading]="assignmentLoading"
        [disabled]="!anySelected"
        (buttonClick)="removeUser()"
      ></app-button>
    </div>

  </div>
</div>

<div class="modal-footer justify-content-end align-items-center pe-2 gap-2">
  <div *ngIf="assignmentLoading" class="progress col">
    <div
      class="progress-bar"
      role="progressbar"
      [style.width.%]="getProgress()"
      [attr.aria-valuenow]="getProgress()"
      aria-valuemin="0"
      aria-valuemax="100">
      {{ getProgress() | number:'1.0-0' }}%
    </div>
  </div>
  <app-button
    [translationKey]="'common.close'"
    [themeStyle]="'secondary'"
    (buttonClick)="activeModal.close(false)"
  ></app-button>
</div>

<ng-template #employeesColumn let-column="column" let-row="row">
  <app-profiled-item-list
      [id]="column.name"
      [wrap]="false"
      [itemImageKey]="'profile_image_url'"
      [itemIdKey]="'user_id'"
      [small]="true"
      [showDeleteCross]="false"
      [employeeSelector]="true"
      [selectedItems]="row.users"
    ></app-profiled-item-list>
</ng-template>
