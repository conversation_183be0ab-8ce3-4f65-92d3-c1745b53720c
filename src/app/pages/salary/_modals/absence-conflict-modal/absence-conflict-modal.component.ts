import {Component, Input, OnInit, TemplateRef, ViewChild} from '@angular/core';
import {StandardImports} from "../../../../@shared/global_import";
import {TablerinoColumn, TablerinoComponent, TablerinoSettings} from '../../../../@shared/components/tablerino/tablerino.component';
import {EmployeeAvailabilityItemResponse} from "../../../../@shared/models/employee.interfaces";
import {BehaviorSubject, firstValueFrom, merge} from "rxjs";
import {NgbActiveModal, NgbModal} from "@ng-bootstrap/ng-bootstrap";
import {ProductService} from "../../../../@shared/services/product.service";
import {TranslateService} from "@ngx-translate/core";
import {UtilsService} from "../../../../@core/utils/utils.service";
import {ProfiledItemListComponent} from "../../../../@shared/components/profiled-item-list/profiled-item-list.component";
import {WorkOrderDetailsComponent, WorkOrderDetailsModal} from "../../../work-orders/components/work-order-details/work-order-details.component";
import {EmployeeService} from "../../../../@shared/services/employee.service";
import {_CRM_EMP_7, _CRM_ORD_113, _CRM_ORD_114, _CRM_PRD_0} from "../../../../@shared/models/input.interfaces";
import {take} from "rxjs/operators";
import {SelectoriniComponent} from "../../../../@shared/components/selectorini/selectorini.component";
import {InternalUserResponse, UserEntityRelationWithUserDataResponse} from "../../../../@shared/models/user.interfaces";
import {OrderService} from "../../../../@shared/services/order.service";
import {StorageService} from "../../../../@core/services/storage.service";

export interface ItemRow extends EmployeeAvailabilityItemResponse {
  selected: boolean;
  disableSelect: boolean;
  disableSelectTooltip: string | null;
}

@Component({
    selector: 'app-import-products-modal',
    templateUrl: './absence-conflict-modal.component.html',
    styleUrl: './absence-conflict-modal.component.css',
    standalone: true,
  imports: [StandardImports, TablerinoComponent, ProfiledItemListComponent, SelectoriniComponent]
})
export class AbsenceConflictModalComponent implements OnInit{
  @Input() dateFrom: Date;
  @Input() dateTo: Date;
  @Input() user: InternalUserResponse;
  items: EmployeeAvailabilityItemResponse[] = [];
  itemRows: ItemRow[] = [];
  columnsSubject = new BehaviorSubject<TablerinoColumn[]>([]);
  tableLoading: boolean = false;
  employees: UserEntityRelationWithUserDataResponse[] = [];
  selectedSubstitutes: UserEntityRelationWithUserDataResponse[] = [];
  selectedRowsSubject = new BehaviorSubject<ItemRow[]>([]);
  tableSettings: TablerinoSettings = {
    checkboxes: true,
    clickableRows: true,
  }
  assignmentLoading: boolean = false;
  anySelected: boolean = false;
  totalOperations: number = 0;
  currentOperation: number = 0;

  showEmployeeInitials: boolean = false;

  @ViewChild('employeesColumn', { static: true }) employeesColumn!: TemplateRef<any>;
  @ViewChild('workOrderTablerino', { static: true }) workOrderTablerino!: TablerinoComponent;

  constructor(public activeModal: NgbActiveModal, private modalService: NgbModal, private employeeService: EmployeeService, private orderService: OrderService, private translate: TranslateService, private utilsService: UtilsService, private storageService: StorageService) {}

  ngOnInit(): void {
    this.storageService.showEmployeeInitials$.subscribe((data) => {
      this.showEmployeeInitials = data;
    });

    this.selectedRowsSubject.subscribe((rows) => {
      this.anySelected = rows.some((row) => row.selected);
    });
    this.initializeColumns();
    this.fetch();
    this.employeeService.getEmployees({paginate: 0}).subscribe((response) => {
      this.employees = response.data.sort((a, b) => {
        if (a.full_name < b.full_name) {
          return -1;
        }
        if (a.full_name > b.full_name) {
          return 1;
        }
        return 0;
      });
    });
  }

  fetch() {
    let params: _CRM_EMP_7 = {
      user_id: this.user.user_id,
      date_from: this.dateFrom,
      date_to: this.dateTo,
    }
    this.tableLoading = true;
    this.employeeService.getEmployeeAssignmentAvailabilityForTimeSlot(params).subscribe((response => {
      this.items = response;
      this.itemRows = this.items.filter((item) => item.work_order).map((item) => ({
        ...item,
        selected: false,
        disableSelect: item.company_id != this.storageService.getSelectedCompanyId(),
        disableSelectTooltip: item.company_id != this.storageService.getSelectedCompanyId() ? 'Dette er en jobb som eies av et annet selskap, og må håndteres manuelt' : '',
      }));
      this.selectedRowsSubject.next([]);
      this.workOrderTablerino.allSelected = false;
      this.tableLoading = false;
    }), error => {
      this.tableLoading = false;
    });
  }

  initializeColumns() {
    this.columnsSubject.next([
      {
        name: 'title',
        labelKey: 'salary.absence.conflictModal.list.job',
        formatter: (row: ItemRow) => row.title,
        sort: false,
        visible: true,
      },
      {
        name: 'executionDate',
        labelKey: 'salary.absence.conflictModal.list.executionDate',
        formatter: (row: ItemRow) => this.formatExecutionDate(row),
        sort: false,
        visible: true,
      },
      {
        name: 'customer',
        labelKey: 'workOrder.list.column.customer',
        formatter: (row: ItemRow) => row.customer_name || '',
        sort: false,
        visible: true,
      },
      {
        name: 'customer',
        labelKey: 'workOrder.list.column.address',
        formatter: (row: ItemRow) => row.address || '',
        sort: false,
        visible: true,
      },
      {
        name: 'assignees',
        labelKey: 'workOrder.list.column.assignees',
        formatter: (row: ItemRow) => row.users,
        sort: false,
        visible: true,
        ngTemplate: this.employeesColumn,
      },
    ]);
  }

  formatExecutionDate(row: ItemRow) {
    let sameDate = row.date_from.toDateString() === row.date_to.toDateString();
    if (sameDate) {
      return `${this.utilsService.formatFullDayAndDate(row.date_from, false)} ${this.utilsService.formatTimeHM(row.date_from)} - ${this.utilsService.formatTimeHM(row.date_to)}`;
    } else {
      return `${this.utilsService.formatFullDayAndDate(row.date_from, false)} ${this.utilsService.formatTimeHM(row.date_from)} - ${this.utilsService.formatFullDayAndDate(row.date_to, false)} ${this.utilsService.formatTimeHM(row.date_to)}`;
    }
  }


  updateSelectedSubstitutes(emps: UserEntityRelationWithUserDataResponse[] | any[]) {
    this.selectedSubstitutes = emps;
  }


  rowClicked(row: ItemRow) {
    let modalRef = this.modalService.open(WorkOrderDetailsComponent, {size: 'xl'});
    modalRef.componentInstance.workOrderId = row.work_order_id;
    modalRef.componentInstance.viewSettings = {
      ...WorkOrderDetailsModal,
      workOrderStandaloneView: true
    };
    merge(modalRef.closed, modalRef.dismissed).pipe(take(1)).subscribe(() => {
      this.fetch();
    });
  }

  async removeUser() {
    this.assignmentLoading = true;
    this.totalOperations = this.selectedRowsSubject.value.length;
    for (let wo of this.selectedRowsSubject.value) {
      this.currentOperation++;
      let payload: _CRM_ORD_114 = {
        work_order_id: wo.work_order_id!,
        user_id: this.user.user_id,
      }
      const res = await firstValueFrom(this.orderService.removeUserFromWorkOrder(payload));
    }
    this.assignmentLoading = false;
    this.selectedSubstitutes = [];
    this.fetch();
  }

  async assignUser(removeAbsenceUser: boolean) {
    this.assignmentLoading = true;
    this.totalOperations = this.selectedRowsSubject.value.length * this.selectedSubstitutes.length;
    for (let wo of this.selectedRowsSubject.value) {
      for (let sub of this.selectedSubstitutes) {
        let payload: _CRM_ORD_113 = {
          work_order_id: wo.work_order_id!,
          user_id: sub.user_id,
        }
        const res = await firstValueFrom(this.orderService.assignUserToWorkOrder(payload));
        this.currentOperation++;
      }
      if (removeAbsenceUser) {
        let payload: _CRM_ORD_114 = {
          work_order_id: wo.work_order_id!,
          user_id: this.user.user_id,
        }
        const removeRes = await firstValueFrom(this.orderService.removeUserFromWorkOrder(payload));
      }
    }
    this.assignmentLoading = false;
    this.selectedSubstitutes = [];
    this.fetch();
  }



  getProgress(): number {
    if (!this.totalOperations || this.totalOperations === 0) return 0;
    return (this.currentOperation / this.totalOperations) * 100;
  }

}
