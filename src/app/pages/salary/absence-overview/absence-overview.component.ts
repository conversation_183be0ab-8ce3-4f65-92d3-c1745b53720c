import {AfterViewInit, ChangeDetectorRef, Component, OnInit, Optional, TemplateRef, ViewChild} from '@angular/core';
import {NgbActiveModal, NgbModal} from '@ng-bootstrap/ng-bootstrap';
import {BehaviorSubject} from "rxjs";
import {TranslateService} from "@ngx-translate/core";
import {formatDateDMY, formatTimeYMD, UtilsService} from "../../../@core/utils/utils.service";
import {OrderService} from "../../../@shared/services/order.service";
import {ActivatedRoute, Router} from "@angular/router";
import {TablerinoColumn, TablerinoComponent} from "../../../@shared/components/tablerino/tablerino.component";
import {EmployeeService} from "../../../@shared/services/employee.service";
import {ResourceService} from "../../../@shared/services/resource.service";
import {StorageService} from "../../../@core/services/storage.service";
import {TrackingService} from "../../../@shared/services/tracking.service";
import {ToastService} from "../../../@core/services/toast.service";
import {EmployeeAbsenceResponse} from "../../../@shared/models/timetracking.interfaces";
import {StandardImports} from "../../../@shared/global_import";
import {EmployeeAbsenceModalComponent} from "../_modals/employee-absence-modal/employee-absence-modal.component";
import {SelectoriniComponent} from "../../../@shared/components/selectorini/selectorini.component";
import {InternalUserResponse} from "../../../@shared/models/user.interfaces";
import {ButtonDoubleComponent} from "../../../@shared/components/button-double/button-double.component";
import {_CRM_TTR_49} from "../../../@shared/models/input.interfaces";

@Component({
    selector: 'app-absence-overview',
    templateUrl: './absence-overview.component.html',
    styleUrls: ['./absence-overview.component.css'],
    standalone: true,
  imports: [StandardImports, TablerinoComponent, SelectoriniComponent, ButtonDoubleComponent]
})
export class AbsenceOverviewComponent implements OnInit, AfterViewInit {
  loading: boolean = false;
  absences: EmployeeAbsenceResponse[] = []
  columnsSubject: BehaviorSubject<TablerinoColumn[]> = new BehaviorSubject<TablerinoColumn[]>([])
  employees: InternalUserResponse[] = [];
  approvingAbsenceLoadingId: number | null = null;
  decliningAbsenceLoadingId: number | null = null;
  hideOldAbsences: boolean = true;

  @ViewChild('statusColumnTemplate', {static: true}) statusColumnTemplate: TemplateRef<any>;

  constructor(public utilsService: UtilsService,
              @Optional() public activeModal: NgbActiveModal,
              private orderService: OrderService,
              private route: ActivatedRoute,
              private modalService: NgbModal,
              private translate: TranslateService,
              private employeeService: EmployeeService,
              private resourceService: ResourceService,
              private storageService: StorageService,
              private trackingService: TrackingService,
              private cdr: ChangeDetectorRef,
              private toastService: ToastService,
              private router: Router,
              ) {
  }

  ngOnInit() {
    this.fetch();
    this.employeeService.getEmployees({}).subscribe((response) => {
      this.employees = response.data.sort((a, b) => {
        if (a.full_name < b.full_name) {
          return -1;
        }
        if (a.full_name > b.full_name) {
          return 1;
        }
        return 0;
      });
    });
  }

  ngAfterViewInit() {
    this.initializeColumns();
    this.cdr.detectChanges();
  }

  fetch() {
    this.loading = true;
    this.trackingService.getCompanyAbsences(new Date().getFullYear()).subscribe((res) => {
      // sort res on user.full_name, and then on active_from
      this.absences = res.sort((a, b) => {
        if (a.user.full_name < b.user.full_name) {
          return -1;
        }
        if (a.user.full_name > b.user.full_name) {
          return 1;
        }
        if (a.active_from < b.active_from) {
          return -1;
        }
        if (a.active_from > b.active_from) {
          return 1;
        }
        return 0;
      });
      this.loading = false;
    }, error => {
      this.loading = false;
    });
  }

  initializeColumns() {
    this.columnsSubject.next([
      {
        name: 'user_full_name',
        labelKey: 'salary.absence.list.employee',
        formatter: (row: EmployeeAbsenceResponse) => row.user.full_name,
        sort: false,
        visible: true,
      },
      {
        name: 'type_name',
        labelKey: 'salary.absence.modal.absenceType',
        formatter: (row: EmployeeAbsenceResponse) => row.activity_name,
        sort: false,
        visible: true,
      },
      {
        name: 'description',
        labelKey: 'salary.absence.modal.description',
        formatter: (row: EmployeeAbsenceResponse) => row.description,
        sort: false,
        visible: true,
      },
      {
        name: 'active_from',
        labelKey: 'salary.activeFrom',
        formatter: (row: EmployeeAbsenceResponse) => formatTimeYMD(row.active_from),
        sort: false,
        visible: true,
      },
      {
        name: 'active_to',
        labelKey: 'salary.activeTo',
        formatter: (row: EmployeeAbsenceResponse) => formatTimeYMD(row.active_to),
        sort: false,
        visible: true,
      },
      {
        name: 'status',
        labelKey: 'Status',
        formatter: (row: EmployeeAbsenceResponse) => '',
        ngTemplate: this.statusColumnTemplate,
        sort: false,
        visible: true,
      },
    ]);
  }

  addAbsence(user: InternalUserResponse | any) {
    let modalRef = this.modalService.open(EmployeeAbsenceModalComponent, {size: 'md'});
    modalRef.componentInstance.user = user;
    modalRef.result.then((res: EmployeeAbsenceResponse) => {
      if (res) {
        this.fetch();
      }
    });
  }

  openAbsenceModal(ea: EmployeeAbsenceResponse) {
    let modalRef = this.modalService.open(EmployeeAbsenceModalComponent, {size: 'md'});
    modalRef.componentInstance.absence = ea;
    modalRef.componentInstance.user = ea.user;
    modalRef.componentInstance.absenceDeleted.subscribe(() => {
      this.fetch();
    })
    modalRef.result.then((res: EmployeeAbsenceResponse) => {
      if (res) {
        this.fetch();
      }
    });
  }

  approveAbsence(ea: EmployeeAbsenceResponse, event: MouseEvent) {
    event.stopPropagation();
    this.approvingAbsenceLoadingId = ea.entry_id;
    let payload: _CRM_TTR_49 = {
      entry_id: ea.entry_id,
      approve: true,
    }
    this.trackingService.updateAbsence(payload).subscribe((res) => {
      this.absences[this.absences.findIndex((e => e.entry_id === ea.entry_id))] = res;
      this.approvingAbsenceLoadingId = null;
    });
  }

  declineAbsence(ea: EmployeeAbsenceResponse, event: MouseEvent) {
    event.stopPropagation();
    this.decliningAbsenceLoadingId = ea.entry_id;
    let payload: _CRM_TTR_49 = {
      entry_id: ea.entry_id,
      decline: true,
    }
    this.trackingService.updateAbsence(payload).subscribe((res) => {
      this.absences[this.absences.findIndex((e => e.entry_id === ea.entry_id))] = res;
      this.decliningAbsenceLoadingId = null;
    });
  }

  protected readonly formatDateDMY = formatDateDMY;
}
