<div class="modal-header">
  <h4 class="modal-title">{{ 'settings.billing.invoices' | translate }}</h4>
  <button type="button" class="btn-close" aria-label="Close" (click)="close()"></button>
</div>
<div class="modal-body">
  <app-tablerino
    [tableName]="'billing-invoices-modal'"
    [columnsSubject]="columnsSubject"
    [tableData]="invoiceRows"
    [settings]="settings"
    [loading]="isLoading"
    [noResultsTranslationKey]="'settings.billing.noInvoices'"
  ></app-tablerino>
</div>
<div class="modal-footer">
  <button type="button" class="btn btn-secondary" (click)="close()">
    {{ 'common.close' | translate }}
  </button>
</div>

<ng-template #pdfButton let-column="column" let-row="row">
  <div class="d-flex align-items-center">
    <app-button
      [translationKey]="'common.download'"
      [buttonType]="'nude'"
      [small]="true"
      [loading]="paymentIdLoading === row.payment_id"
      (buttonClick)="downloadInvoice(row)"
    ></app-button>
  </div>
</ng-template>
