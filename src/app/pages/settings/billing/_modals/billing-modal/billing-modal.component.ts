import {Component, Input, OnInit, TemplateRef, ViewChild} from '@angular/core';
import { NgbActiveModal } from '@ng-bootstrap/ng-bootstrap';
import {TablerinoColumn, TablerinoComponent, TablerinoSettings} from "../../../../../@shared/components/tablerino/tablerino.component";
import {TranslateModule} from "@ngx-translate/core";
import {NgIf} from "@angular/common";
import {OrderPaymentResponse} from "../../../../../@shared/models/payment.interfaces";
import {BehaviorSubject, pairwise, Subject} from "rxjs";
import {OrderService} from "../../../../../@shared/services/order.service";
import {PaymentService} from "../../../../../@shared/services/payment.service";
import {StandardImports} from "../../../../../@shared/global_import";
import {PaginationContainer} from "../../../../../@shared/models/global.interfaces";
import {PaginationResponse} from "../../../../../@shared/models/response.interfaces";
import {currencyFormat, paymentStatusBadge, UtilsService} from "../../../../../@core/utils/utils.service";
import {pagination_input} from "../../../../../@shared/models/input.interfaces";
import {takeUntil} from "rxjs/operators";
import {CompanyService} from "../../../../../@shared/services/company.service";

@Component({
  selector: 'app-billing-modal',
  templateUrl: './billing-modal.component.html',
  standalone: true,
  imports: [
    TablerinoComponent,
    TranslateModule,
    NgIf,
    StandardImports
  ],
  styleUrls: ['./billing-modal.component.css']
})
export class BillingModalComponent implements OnInit {
  invoiceDataResponse: PaginationResponse<OrderPaymentResponse[]>
  invoices: OrderPaymentResponse[] = [];
  invoiceRows: OrderPaymentResponse[] = [];
  cancelCurrentRequest$ = new Subject<void>();
  paginationSubject: BehaviorSubject<PaginationContainer> = new BehaviorSubject<PaginationContainer>({page: 1, limit: 25, paginate: 1, totalPages: 0, totalItems: 0});
  columnsSubject: BehaviorSubject<TablerinoColumn[]> = new BehaviorSubject<TablerinoColumn[]>([]);
  settings: TablerinoSettings = {
    clickableRows: true,
    checkboxes: false
  };
  paymentIdLoading: number | null = null;
  isLoading: boolean = false;

  @ViewChild('pdfButton', {static: true}) pdfButton: TemplateRef<any>;

  constructor(public activeModal: NgbActiveModal, private paymentService: PaymentService, private utilsService: UtilsService, private companyService: CompanyService) {}

  ngOnInit() {
    this.paginationSubject.pipe(pairwise()).subscribe((paginationDetails) => {
      if (paginationDetails[0].page !== paginationDetails[1].page) {
        this.fetch();
      }
    });
    this.initializeColumns();
    this.fetch();
  }

  fetch() {
    this.isLoading = true;
    this.cancelCurrentRequest$.next();
    // In a real implementation, you would call a service to get the invoices
    // For now, we'll simulate a delay and some sample data
    let params: pagination_input = {
      paginate: 1,
      page: this.paginationSubject.value.page,
      limit: this.paginationSubject.value.limit,
    }
    this.companyService.getAdminSubscriptionPayments(params).pipe(takeUntil(this.cancelCurrentRequest$)).subscribe(res => {
      this.invoiceDataResponse = res;
      this.invoices = res.data;
      this.paginationSubject.next({
        ...this.paginationSubject.value,
        totalItems: res.total_items,
        totalPages: res.total_pages
      });

      this.invoiceRows = this.invoices.map(invoice => ({
        ...invoice,
        selected: false
      }));
      this.isLoading = false;
    });
  }

  initializeColumns(): void {
    this.columnsSubject.next([
      {
        name: 'payment_number',
        labelKey: 'ID',
        formatter: (payment: OrderPaymentResponse) => '#' + payment.payment_number,
        sort: true,
        visible: true,
      },
      {
        name: 'payment_method_id',
        labelKey: 'payments.list.paymentMethod',
        formatter: (payment: OrderPaymentResponse) => payment.payment_method_name,
        sort: true,
        visible: true,
      },
      {
        name: 'payment_status_id',
        labelKey: 'payments.list.status',
        formatter: (payment: OrderPaymentResponse) => paymentStatusBadge(payment),
        sort: true,
        visible: true,
      },
      {
        name: 'auto_send_at',
        labelKey: 'settings.billing.dueDate',
        formatter: (payment: OrderPaymentResponse) => this.formatDueDate(payment),
        sort: false,
        visible: true,
      },
      {
        name: 'total_amount_inc_vat',
        labelKey: 'payments.list.totalAmount',
        formatter: (payment: OrderPaymentResponse) => currencyFormat(payment.total_amount_inc_vat),
        sort: true,
        visible: true,
      },
      {
        name: 'captured_at',
        labelKey: 'payments.list.capturedAt',
        formatter: (payment: OrderPaymentResponse) => this.utilsService.displayDate(payment.captured_at, false),
        sort: true,
        visible: true,
      },
      {
        name: 'actions',
        labelKey: 'settings.billing.actions',
        formatter: (payment: OrderPaymentResponse) => '',
        sort: false,
        visible: true,
        buttonColumn: true,
        hideHeaderName: true,
        ngTemplate: this.pdfButton
      }
    ]);
  }

    formatDueDate(payment: OrderPaymentResponse) {
    if (payment.payment_method_id === 10) {
      if (payment.payment_sent_at) {
        let dueDate = new Date(payment.payment_sent_at);
        dueDate.setDate(dueDate.getDate() + payment.invoice_due_date_days);
        return this.utilsService.displayDate(dueDate, false);
      } else if (payment.auto_send_at) {
        let dueDate = new Date(payment.auto_send_at);
        dueDate.setDate(dueDate.getDate() + payment.invoice_due_date_days);
        return this.utilsService.displayDate(dueDate, false);
      } else {
        return '';
      }
    } else if (payment.payment_sent_at) {
      return this.utilsService.displayDate(payment.payment_sent_at, false);
    } else {
      return this.utilsService.displayDate(payment.auto_send_at, false);
    }
  }

  close() {
    this.activeModal.dismiss();
  }

  downloadInvoice(payment: OrderPaymentResponse) {
    this.paymentIdLoading = payment.payment_id;
    this.paymentService.getPaymentInvoicePdfFromTripletex(payment.payment_id).subscribe((response) => {
      this.paymentIdLoading = null;
      const fileUrl = URL.createObjectURL(response);
      const link = document.createElement('a');
      link.href = fileUrl;
      link.download = 'invoice_' + payment.order_number + '.pdf';
      link.click();
      URL.revokeObjectURL(fileUrl);
      link.remove();
    }, error => {
      this.paymentIdLoading = null;
    });
  }
}
