<div class="container-fluid">
  <div class="row">
    <!-- Credit Card Information -->
    <app-card
      [labelKey]="'settings.billing.paymentMethod'"
      [margin]="'mb-2'"
      [backgroundColor]="'rgb(252, 252, 252)'"
      [buttonHeader]="true"
      class="col-12">
      <app-button
        buttonHeader
        [buttonType]="'nude'"
        [iconClass]="'fa-regular fa-clock-rotate-left'"
        [translationKey]="'settings.billing.paymentHistory'"
        (buttonClick)="openInvoicesModal()"
      ></app-button>
      <ng-container cardcontent>
        <div class="row">
          <div class="col-12">
            <!-- Loading state -->
            <div *ngIf="isLoading" class="d-flex justify-content-center">
              <div class="spinner-border" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
            </div>

            <!-- No card state -->
            <div *ngIf="!isLoading && cardState === 'no-card'">
              <div class="p-3 bg-light border rounded text-center">
                <p class="mb-3">{{ (subscriptionPayment ? 'settings.billing.noCardMessage' : 'settings.billing.noMainPayment') | translate }}</p>
                <button type="button" class="btn btn-primary" (click)="addCreditCard()" [disabled]="isSaving || !subscriptionPayment">
                  <span *ngIf="isSaving" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                  {{ 'settings.billing.addCard' | translate }}
                </button>
              </div>
            </div>

            <!-- Adding card state -->
            <div *ngIf="!isLoading && cardState === 'adding-card'">
              <div class="p-3 bg-light border rounded text-center">
                <p class="mb-4">{{ 'settings.billing.redirectingMessage' | translate }}</p>
                <div class="spinner-border" role="status">
                  <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-3">{{ 'settings.billing.doNotCloseWindow' | translate }}</p>
              </div>
            </div>

            <!-- Verifying payment state -->
            <div *ngIf="!isLoading && cardState === 'verifying-payment'">
              <div class="p-3 bg-light border rounded text-center">
                <p class="mb-4">{{ 'settings.billing.verifyingPayment' | translate }}</p>
                <div class="spinner-border" role="status">
                  <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-3">{{ 'settings.billing.verifyingPaymentMessage' | translate }}</p>
              </div>
            </div>

            <!-- Card saved state -->
            <div *ngIf="!isLoading && cardState === 'card-saved' && savedCard">
              <div class="p-3 bg-light border rounded">
                <div class="d-flex justify-content-between align-items-center mb-2">
                  <h5 class="mb-0">{{ 'Visa' }}</h5>
                  <span class="badge bg-success">{{ 'settings.billing.active' | translate }}</span>
                </div>
                <p>{{ savedCard.cardNumber }}</p>
                <div class="d-flex justify-content-between">
                  <div>
                    <small class="text-muted">{{ 'settings.billing.expires' | translate }}</small>
                    <p class="mb-0">{{ savedCard.cardExpiry }}</p>
                  </div>
                </div>
              </div>
              <!-- <div class="d-flex justify-content-end">
                <button type="button" class="btn btn-outline-primary" (click)="changeCreditCard()" [disabled]="isSaving">
                  <span *ngIf="isSaving" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                  {{ 'settings.billing.changeCard' | translate }}
                </button>
              </div> -->
            </div>

            <!-- Error state -->
            <div *ngIf="!isLoading && cardState === 'error'" class="my-4">
              <div class="mb-4 p-3 bg-danger-subtle border border-danger rounded">
                <i class="fa-solid fa-triangle-exclamation me-2"></i>
                {{ errorMessage | translate }}
              </div>
              <div class="d-flex justify-content-end">
                <button type="button" class="btn btn-primary" (click)="addCreditCard()" [disabled]="isSaving">
                  <span *ngIf="isSaving" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
                  {{ 'settings.billing.tryAgain' | translate }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </ng-container>
    </app-card>

  </div>
</div>
