import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TemplateRef, ViewChild } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { ToastService } from 'src/app/@core/services/toast.service';
import { StorageService } from 'src/app/@core/services/storage.service';
import { SettingsService } from 'src/app/@shared/services/settings.service';
import {BehaviorSubject, pairwise, Subject, Subscription, interval} from 'rxjs';
import { TablerinoColumn, TablerinoComponent, TablerinoSettings } from 'src/app/@shared/components/tablerino/tablerino.component';
import {currencyFormat, paymentStatusBadge, UtilsService} from 'src/app/@core/utils/utils.service';
import { TranslateModule } from '@ngx-translate/core';

import { CardComponent } from 'src/app/@shared/components/layout/card/card.component';
import { ActivatedRoute, Router } from '@angular/router';
import { _CRM_PAY_17 } from 'src/app/@shared/models/input.interfaces';
import { OrderPaymentResponse } from 'src/app/@shared/models/payment.interfaces';
import {CompanyService} from "../../../@shared/services/company.service";
import {PaginationResponse} from "../../../@shared/models/response.interfaces";
import {PaginationContainer} from "../../../@shared/models/global.interfaces";
import {takeUntil, take} from "rxjs/operators";
import {pagination_input} from "../../../@shared/models/input.interfaces";
import {PaymentService} from "../../../@shared/services/payment.service";
import {ButtonComponent} from "../../../@shared/components/button/button.component";
import {StandardImports} from "../../../@shared/global_import";
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { BillingModalComponent } from './_modals/billing-modal/billing-modal.component';

@Component({
  selector: 'app-billing',
  templateUrl: './billing.component.html',
  styleUrls: ['./billing.component.css'],
  standalone: true,
  imports: [StandardImports, CardComponent]
})
export class BillingComponent implements OnInit, OnDestroy {
  creditCardForm: FormGroup;
  isLoading = false;
  isSaving = false;

  // Credit card states
  cardState: 'no-card' | 'adding-card' | 'card-saved' | 'error' | 'verifying-payment' = 'no-card';
  errorMessage: string = '';
  savedCard: {cardExpiry: string, cardNumber: string} | null = null;
  subscriptionPayment: OrderPaymentResponse | null = null;

  cancelCurrentRequest$ = new Subject<void>();

  // Polling variables
  private pollingSubscription: Subscription | null = null;
  private pollingAttempts = 0;
  private maxPollingAttempts = 10; // Maximum number of polling attempts
  private pollingInterval = 3000; // Polling interval in milliseconds (3 seconds)

  // Tablerino configuration


  @ViewChild('downloadTemplate', {static: true}) downloadTemplate: TemplateRef<any>;

  constructor(
    private formBuilder: FormBuilder,
    private settingsService: SettingsService,
    private toastService: ToastService,
    private storageService: StorageService,
    private utilsService: UtilsService,
    private route: ActivatedRoute,
    private paymentService: PaymentService,
    private router: Router,
    private companyService: CompanyService,
    private modalService: NgbModal
  ) {
  }

  ngOnInit(): void {
    this.loadSubscriptionPayment();

    // Check for URL parameters that might be returned from the payment provider
    this.route.queryParams.subscribe(params => {
      if (params['from_quickpay'] === 'true') {
        // User is returning from QuickPay - start polling for payment status
        this.cardState = 'verifying-payment';
        this.startPollingForPaymentStatus();

        // Clear the URL parameters
        this.router.navigate([], {
          relativeTo: this.route,
          queryParams: {},
          replaceUrl: true
        });
      } else if (params['success'] === 'true') {
        this.cardState = 'card-saved';
        this.loadCreditCardInfo();
        this.loadSubscriptionPayment(); // Reload subscription after success
        this.toastService.successToast('settings.billing.cardAddedSuccess');

        // Clear the URL parameters
        this.router.navigate([], {
          relativeTo: this.route,
          queryParams: {},
          replaceUrl: true
        });
      } else if (params['error'] === 'true') {
        this.cardState = 'error';
        this.errorMessage = params['message'] || 'settings.billing.genericError';
        this.toastService.errorToast(this.errorMessage);

        // Clear the URL parameters
        this.router.navigate([], {
          relativeTo: this.route,
          queryParams: {},
          replaceUrl: true
        });
      } else {
        this.loadCreditCardInfo();
      }
    });
  }

  loadSubscriptionPayment(): void {
    this.isLoading = true;
    this.companyService.getAdminMainSubscriptionPayment().subscribe({
      next: (payment) => {
        this.subscriptionPayment = payment;

        // Check if payment exists and has an active payment schedule
        const hasActiveSchedule = payment && payment.subscription_active;

        if (hasActiveSchedule) {
          this.cardState = 'card-saved';
          this.updateCardInfoFromSubscription();
        } else {
          this.cardState = 'no-card';
        }

        this.isLoading = false;
      },
      error: () => {
        this.cardState = 'no-card';
        this.isLoading = false;
      }
    });
  }





  loadCreditCardInfo(): void {
    // If we already have the subscription payment, use it
    if (this.subscriptionPayment) {
      this.updateCardInfoFromSubscription();
      return;
    }

    // Otherwise, load the subscription payment
    this.isLoading = true;
    this.companyService.getAdminMainSubscriptionPayment().subscribe({
      next: (payment) => {
        this.subscriptionPayment = payment;
        this.updateCardInfoFromSubscription();
        this.isLoading = false;
      },
      error: () => {
        if (this.cardState !== 'error') {
          this.cardState = 'no-card';
        }
        this.savedCard = null;
        this.isLoading = false;
      }
    });
  }

  updateCardInfoFromSubscription(): void {
    if (this.subscriptionPayment && this.subscriptionPayment.subscription_active) {
      // Extract card info from the subscription payment
      this.savedCard = {
        cardNumber: '**** **** **** ' + (this.subscriptionPayment.card_last4 || '****'),
        cardExpiry: this.subscriptionPayment.card_expiration || 'MM/YY',
      };

      this.cardState = 'card-saved';
    } else if (this.cardState !== 'error') {
      this.cardState = 'no-card';
      this.savedCard = null;
    }
  }

  addCreditCard(): void {
    this.cardState = 'adding-card';
    this.isSaving = true;

    // Create a subscription using the company service
    const payload: _CRM_PAY_17 = {
      as_admin: true,
      payment_id: this.subscriptionPayment?.payment_id,
      payment_method_id: 14
    };

    this.companyService.createAdminSubscription(payload).subscribe({
      next: (response) => {
        // Check if the response contains a redirect URL
        if (response && response.url) {
          // Redirect the user to the payment provider URL
          window.location.href = response.url;

          this.isSaving = false;
        }
      },
      error: () => {
        // Error handling
        this.cardState = 'error';
        this.errorMessage = 'settings.billing.paymentProviderError';
        this.toastService.errorToast(this.errorMessage);
        this.isSaving = false;
      }
    });
  }

  changeCreditCard(): void {
    if (!this.subscriptionPayment) {
      return;
    }
    // Similar to addCreditCard, but for changing an existing card
    this.cardState = 'adding-card';
    this.isSaving = true;

    // Create a subscription using the company service
    const payload: _CRM_PAY_17 = {
      as_admin: true,
      payment_id: this.subscriptionPayment?.payment_id,
      payment_method_id: 14
    };

    payload.payment_id = this.subscriptionPayment.payment_id;

    this.companyService.createAdminSubscription(payload).subscribe({
      next: (response) => {
        // Check if the response contains a redirect URL
        if (response && response.url) {
          // Redirect the user to the payment provider URL
          window.location.href = response.url;
        } else {
          // If no URL is provided, update the UI as before
          this.cardState = 'card-saved';
          this.loadSubscriptionPayment(); // Reload the subscription to get updated info
          this.toastService.successToast('settings.billing.cardAddedSuccess');
          this.isSaving = false;
        }
      },
      error: () => {
        // Error handling
        this.cardState = 'error';
        this.errorMessage = 'settings.billing.paymentProviderError';
        this.toastService.errorToast(this.errorMessage);
        this.isSaving = false;
      }
    });
  }

  openInvoicesModal() {
    const modalRef = this.modalService.open(BillingModalComponent, {
      size: 'xl',
      centered: true
    });
  }

  ngOnDestroy(): void {
    // Clean up subscriptions
    this.cancelCurrentRequest$.next();
    this.cancelCurrentRequest$.complete();
    this.stopPolling();
  }

  startPollingForPaymentStatus(): void {

    // Stop any existing polling
    this.stopPolling();

    // Reset polling attempts
    this.pollingAttempts = 0;

    // Start polling
    this.pollingSubscription = interval(this.pollingInterval)
      .pipe(
        takeUntil(this.cancelCurrentRequest$),
        take(this.maxPollingAttempts)
      )
      .subscribe(() => {
        this.pollingAttempts++;
        this.checkPaymentStatus();
      });
  }

  stopPolling(): void {
    if (this.pollingSubscription) {
      this.pollingSubscription.unsubscribe();
      this.pollingSubscription = null;
    }
  }

  checkPaymentStatus(): void {
    this.companyService.getAdminMainSubscriptionPayment().subscribe({
      next: (payment) => {
        this.subscriptionPayment = payment;

        // Check if payment exists and has an active subscription
        const hasActiveSubscription = payment && payment.subscription_active;

        if (hasActiveSubscription) {
          // Payment is confirmed, stop polling
          this.stopPolling();
          this.cardState = 'card-saved';
          this.updateCardInfoFromSubscription();
          this.toastService.successToast('settings.billing.cardAddedSuccess');
        } else if (this.pollingAttempts >= this.maxPollingAttempts) {
          // Max polling attempts reached, show error
          this.stopPolling();
          this.cardState = 'error';
          this.errorMessage = 'settings.billing.paymentVerificationTimeout';
          this.toastService.errorToast(this.errorMessage);
        }
        // If not confirmed yet and max attempts not reached, continue polling
      },
      error: () => {
        if (this.pollingAttempts >= this.maxPollingAttempts) {
          // Max polling attempts reached, show error
          this.stopPolling();
          this.cardState = 'error';
          this.errorMessage = 'settings.billing.paymentVerificationError';
          this.toastService.errorToast(this.errorMessage);
        }
        // If max attempts not reached, continue polling
      }
    });
  }
}
