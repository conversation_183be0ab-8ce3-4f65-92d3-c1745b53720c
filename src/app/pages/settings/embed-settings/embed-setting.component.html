<div class="container-fluid">
  <div class="row">
    <div class="col-12">
      <app-card
      [labelKey]="'embedSettings.list.subheader' | translate"
      [margin]="'mb-3'"
      [backgroundColor]="'rgb(252, 252, 252)'"
      [buttonHeader]="true">
      <div buttonHeader>
        <app-button [translationKey]="'embedSettings.list.createButton'" (buttonClick)="createEmbed()"></app-button>
        </div>
        <div cardcontent class="row d-flex align-items-center embed-element m-0 mb-3 ps-2" *ngFor="let embed of companyEmbeds">
          <div class="col-1 d-flex justify-content-center">
            <i class="fa-regular fa-globe fa-2xl"></i>
          </div>
          <div class="col ms-auto d-flex align-items-center">
            <span>{{embed.embed_name}}</span>
          </div>
          <div class="col-1 d-flex justify-content-end align-items-center">
            <app-button class="me-1" [buttonType]="'nude'"  [translationKey]="'embedSettings.list.editButton'" [small]="true" (buttonClick)="onSelectedItem(embed)"></app-button>
            <delete-button [trashIconSize]="18" (delete)="deleteEmbed(embed)"></delete-button>
          </div>
        </div>
      </app-card>


      <app-card
      [labelKey]="'settings.resources.resourceSettings'"
      [margin]="'mb-2'">
      <ng-container cardcontent>
        <form *ngIf="resourceForm" [formGroup]="resourceForm">
          <div class="form-group mb-3">
            <label>{{ "settings.resources.resourceFormTitle" | translate }}</label>
            <div class="col">
              <div class="col">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="monday" name="monday" value="Monday" formControlName="monday">
                  <label class="form-check-label" for="monday">{{ "settings.resources.monday" | translate }}</label>
                </div>
              </div>
              <div class="col">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="tuesday" name="tuesday" value="Tuesday" formControlName="tuesday">
                  <label class="form-check-label" for="tuesday">{{ "settings.resources.tuesday" | translate }}</label>
                </div>
              </div>
              <div class="col">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="wednesday" name="wednesday" value="Wednesday" formControlName="wednesday">
                  <label class="form-check-label" for="wednesday">{{ "settings.resources.wednesday" | translate }}</label>
                </div>
              </div>
              <div class="col">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="thursday" name="thursday" value="Thursday" formControlName="thursday">
                  <label class="form-check-label" for="thursday">{{ "settings.resources.thursday" | translate }}</label>
                </div>
              </div>
              <div class="col">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="friday" name="friday" value="Friday" formControlName="friday">
                  <label class="form-check-label" for="friday">{{ "settings.resources.friday" | translate }}</label>
                </div>

              </div>
              <div class="col">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="saturday" name="saturday" value="Saturday" formControlName="saturday">
                  <label class="form-check-label" for="saturday">{{ "settings.resources.saturday" | translate }}</label>


                </div>
              </div>
              <div class="col">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" id="sunday" name="sunday" value="Sunday" formControlName="sunday">
                  <label class="form-check-label" for="sunday">{{ "settings.resources.sunday" | translate }}</label>
                </div>

              </div>
            </div>
          </div>

          <div class="form-group mb-3" *ngIf="false">
            <label>What hours are your business open?</label>
            <div class="row">
              <div class="col-md-6">
                <label>Open time:</label>
                <input
                  type="time"
                  class="form-control"
                  formControlName="open_from" >
              </div>
              <div class="col-md-6">
                <label>Close time:</label>
                <input
                  type="time"
                  class="form-control"
                  formControlName="open_to" >




              </div>
            </div>
          </div>

          <div class="form-group row mb-3">
            <div class="col-md-6">
              <label>{{"settings.resources.orderLimitPerDayLabel" | translate}}:</label>
              <app-input
                [type]="'number'"
                [hideDecimals]="true"
                [editMode]="true"
                [control]="getFormControl(resourceForm, 'threshold_2')"
                (valueChange)="onOrderLimitChange('threshold_2')"
              ></app-input>
            </div>
            <div class="col-md-6">
              <label>{{"settings.resources.orderWarningLimitLabel" | translate}}:</label>
              <app-input
                [type]="'number'"
                [hideDecimals]="true"
                [editMode]="true"
                [control]="getFormControl(resourceForm, 'threshold_1')"
                (valueChange)="onOrderLimitChange('threshold_1')"
              ></app-input>

            </div>
          </div>
          <div class="form-group m">
            <button
              type="submit"
              class="float-end btn btn-primary float-right"
              [disabled]="loading || !resourceForm.valid"
              (click)="onSubmit()"
            >{{"common.save" | translate}}</button>
          </div>
        </form>
      </ng-container>
    </app-card>
  </div>
  </div>
</div>
