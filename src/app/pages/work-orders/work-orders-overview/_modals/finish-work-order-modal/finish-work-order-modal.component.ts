import {Component, DestroyRef, inject, Input} from '@angular/core';
import {ButtonComponent} from "../../../../../@shared/components/button/button.component";
import {DecimalPipe, NgIf} from "@angular/common";
import {
  TablerinoColumn,
  TablerinoComponent,
  TablerinoSettings
} from "../../../../../@shared/components/tablerino/tablerino.component";
import {AffiliateResponse} from "../../../../../@shared/models/affiliate.interfaces";
import {BehaviorSubject, combineLatest, firstValueFrom, forkJoin} from "rxjs";
import {NgbActiveModal} from "@ng-bootstrap/ng-bootstrap";
import {AffiliateService} from "../../../../../@shared/services/affiliate.service";
import {CustomerService} from "../../../../../@shared/services/customer.service";
import {TranslateService} from "@ngx-translate/core";
import {_CRM_AFF_0, _CRM_CUS_1, _CRM_ORD_12, _CRM_ORD_31} from "../../../../../@shared/models/input.interfaces";
import {WorkOrderRow} from "../../work-orders-overview.component";
import {
  OrderLineResponse,
  QuantityProposalOrderLineResponse,
  WorkOrderResponse
} from "../../../../../@shared/models/order.interfaces";
import {FormControl} from "@angular/forms";
import {OrderService} from "../../../../../@shared/services/order.service";
import {UtilsService} from "../../../../../@core/utils/utils.service";
import {ToastService} from "../../../../../@core/services/toast.service";
import {takeUntilDestroyed} from "@angular/core/rxjs-interop";
import {StandardImports} from "../../../../../@shared/global_import";

@Component({
  selector: 'app-finish-work-order-modal',
  templateUrl: './finish-work-order-modal.component.html',
  styleUrl: './finish-work-order-modal.component.css',
  standalone: true,
  imports: [StandardImports]
})
export class FinishWorkOrderModalComponent {
  @Input() selectedRows: WorkOrderResponse[] = [];

  processing: boolean = false;
  progress: number = 0;

  private destroyRef = inject(DestroyRef);

  constructor(
    public activeModal: NgbActiveModal,
    public utilsService: UtilsService,
    private orderService: OrderService,
    private toastService: ToastService
  ) {}

  ngOnInit() {
    if (!this.selectedRows || this.selectedRows.length === 0) {
      // Handle error or close modal if no work orders were passed.
      console.error('No work orders provided.');
      this.activeModal.close(false);
    }
  }

  // Sequentially finish each work order.
  async finishAllWorkOrders() {
    this.processing = true;
    this.progress = 0;
    const total = this.selectedRows.length;

    // Start a progress simulation that increases the progress gradually.
    const progressInterval = setInterval(() => {
      if (this.progress < 90) { // Only increment until 90%
        this.progress += 10;
      }
    }, 200);

    for (let i = 0; i < total; i++) {
      const workOrder = this.selectedRows[i];

      // Fetch quantity proposals for this work order.
      const proposals: QuantityProposalOrderLineResponse[] = await firstValueFrom(
        this.orderService.getWorkOrderQuantityProposal(workOrder.work_order_id)
      );

      // Build update requests for the proposals.
      const requests = proposals.map((ol) => {
        const trackedLine = workOrder.order_lines
          ? workOrder.order_lines.find(line => line.track_time)
          : undefined;
        const finalQuantity = trackedLine ? trackedLine.quantity : ol.quantity;
        const finalVatRateId = trackedLine ? trackedLine.vat_rate_id : (ol as any).vat_rate_id;
        const finalUnitId = trackedLine ? trackedLine.unit_id : (ol as any).unit_id;

        if (ol.order_line_id === null) {
          const payload: _CRM_ORD_12 = {
            order_id: workOrder.order_id,
            product_id: ol.product_id,
            order_line_name: ol.order_line_name,
            unit_price_inc_vat: ol.precalculated_unit_price_inc_vat,
            vat_rate_id: finalVatRateId,
            unit_id: finalUnitId,
            quantity: finalQuantity,
            price_rule_ids: ol.price_rule_ids,
            comment: ol.comment,
            work_order_id: workOrder.work_order_id,
          };
          return this.orderService.addOrderLineAsCompany(payload);
        } else {
          const payload: _CRM_ORD_31 = {
            order_line_id: ol.order_line_id,
            quantity: finalQuantity
          };
          return this.orderService.updateOrderLineAsCompany(payload);
        }
      });

      // Wait for the update requests (if any) to complete.
      if (requests.length > 0) {
        await firstValueFrom(forkJoin(requests));
      }

      // Now finish the work order.
      // await firstValueFrom(this.orderService.finishWorkOrder(workOrder.work_order_id));

      // Update progress after processing each work order.
      this.progress = Math.round(((i + 1) / total) * 100);
    }

    clearInterval(progressInterval);
    // Ensure progress is set to 100% at the end.
    this.progress = 100;
    this.toastService.successToast('updated');
    this.processing = false;
    this.activeModal.close(true);
  }

  cancel() {
    this.activeModal.dismiss();
  }
}
